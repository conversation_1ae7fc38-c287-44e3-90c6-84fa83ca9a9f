import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import createIntlMiddleware from "next-intl/middleware";
import { routing } from "./i18n/routing";

const intlMiddleware = createIntlMiddleware(routing);

export async function middleware(request: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req: request, res });

  const {
    data: { session },
  } = await supabase.auth.getSession();

  // Protected routes pattern - check for both localized and non-localized paths
  const protectedRoutes = ['/dashboard', '/subjects', '/questions', '/profile', '/subscription', '/progress'];
  const isProtectedRoute = protectedRoutes.some(route => {
    return request.nextUrl.pathname.startsWith(route) || 
           request.nextUrl.pathname.startsWith('/es' + route) ||
           request.nextUrl.pathname.startsWith('/en' + route);
  });

  // Auth routes pattern - check for both localized and non-localized paths
  const authRoutes = ['/login', '/register'];
  const isAuthRoute = authRoutes.some(route => {
    return request.nextUrl.pathname.startsWith(route) ||
           request.nextUrl.pathname.startsWith('/es' + route) ||
           request.nextUrl.pathname.startsWith('/en' + route);
  });

  // Redirect if accessing protected route without session
  if (isProtectedRoute && !session) {
    const loginUrl = new URL('/login', request.url);
    return NextResponse.redirect(loginUrl);
  }
  
  // Redirect if accessing auth route with active session
  if (isAuthRoute && session) {
    const dashboardUrl = new URL('/dashboard', request.url);
    return NextResponse.redirect(dashboardUrl);
  }

  return intlMiddleware(request);
}

export const config = {
  matcher: [
    '/',
    '/(es|en)/:path*',
    '/dashboard/:path*',
    '/subjects/:path*',
    '/questions/:path*',
    '/profile/:path*',
    '/progress/:path*',
    '/login',
    '/register',
    '/subscription/:path*',
  ],
};