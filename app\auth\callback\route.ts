import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');

  if (code) {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    try {
      const { data: { session }, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('Error exchanging code for session:', error);
        return NextResponse.redirect(requestUrl.origin + '/login?error=auth_error');
      }

      if (session) {
        console.log('Session established successfully:', session.user.id);
        return NextResponse.redirect(requestUrl.origin + '/dashboard');
      }
    } catch (error) {
      console.error('Error in auth callback:', error);
      return NextResponse.redirect(requestUrl.origin + '/login?error=auth_error');
    }
  }

  // If no code or session establishment failed, redirect to login
  return NextResponse.redirect(requestUrl.origin + '/login');
}