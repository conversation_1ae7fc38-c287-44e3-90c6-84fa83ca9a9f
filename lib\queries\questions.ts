// Client-side queries for reading questions data
import { supabase } from '@/lib/supabase/client';
import { Database } from '../supabase/database.types';
import { InstitutionStats, SubjectStats, ProcessedResponse } from '../types/progress';

// Internal interface for building institution stats with Map
interface InternalInstitutionStats {
  institution_id: string;
  institution_name: string;
  subjects: Map<number, SubjectStats>;
  total_answers: number;
  total_correct: number;
  total_hours: number;
  total_level: number;
  subjects_count: number;
  average_accuracy: number;
  average_level: number;
}

interface EnrichedResponse {
  id: number;
  user_id: string;
  question_id: number;
  response: number;
  was_correct: boolean;
  created_at: string;
  questions?: Database['public']['Tables']['questions']['Row'];
}

interface EnrichedProgress {
  id: number;
  user_id: string;
  subject_id: number;
  correct_answers: number;
  incorrect_answers: number;
  last_updated: string;
  user_level: number;
  hours: number;
  subjects?: Database['public']['Tables']['subjects']['Row'];
}



interface ChartDataPoint {
  date: string;
  [subject: string]: string | number;
}

interface CumulativeStats {
  [subject: string]: {
    correct: number;
    total: number;
  };
}

interface DateGroups {
  [date: string]: {
    [subject: string]: {
      correct: number;
      total: number;
    };
  };
}

export async function getQuestionCount(subjectId: number): Promise<number> {
  const { count } = await supabase
    .from('questions')
    .select('*', { count: 'exact', head: true })
    .eq('subject_id', subjectId);

  return count || 0;
}

export async function getNextQuestion(userId: string, subjectId: number) {
  const { data: progressData } = await supabase
    .from('user_progress')
    .select('*')
    .eq('user_id', userId)
    .eq('subject_id', subjectId)
    .single();

  const { data, error } = await supabase.rpc('get_next_question', {
    user_id_input: userId,
    user_level_input: progressData ? progressData.user_level : 5,
    subject_id_input: subjectId
  });

  if (error) throw error;
  if (data && data.length > 0) {
    const question = data[0];
    return question;
  }

  return null;
}

// Get question with subject and topic information
export async function getQuestionWithDetails(userId: string, subjectId: number) {
  try {

    // Get the next question
    const question = await getNextQuestion(userId, subjectId);
    if (!question) {
      return null;
    }

    // Get subject information
    const { data: subjectData, error: subjectError } = await supabase
      .from('subjects')
      .select('id, name, institution_id')
      .eq('id', subjectId)
      .single();

    if (subjectError) {
      console.error('Error fetching subject:', subjectError);
    }

    // Get topic information if topic_id exists
    let topicData = null;
    if (question.topic_id) {
      const { data: topic, error: topicError } = await supabase
        .from('topics')
        .select('id, name')
        .eq('id', question.topic_id)
        .single();

      if (topicError) {
        console.error('Error fetching topic:', topicError);
      } else {
        topicData = topic;
      }
    }

    return {
      ...question,
      subject: subjectData,
      topic: topicData
    };
  } catch (error) {
    console.error('Error in getQuestionWithDetails:', error);
    return null;
  }
}

const clampLevel = (level: number): number => Math.max(1, Math.min(10, level));

async function updateRatings(userId: string, questionId: number, subjectId: number, wasCorect: boolean) {
  const { data: questionData } = await supabase
    .from('questions')
    .select('*')
    .eq('id', questionId)
    .single();
  const question = questionData as Database['public']['Tables']['questions']['Row'];

  const { data: userProgressData } = await supabase
    .from('user_progress')
    .select('*')
    .eq('user_id', userId)
    .eq('subject_id', subjectId)
    .single();
  const userProgress = userProgressData as Database['public']['Tables']['user_progress']['Row'];

  // Elo updation
  const K_USER = 0.25;
  const K_QUESTION = 0.07;

  // Probabilidad de éxito esperada
  const P = 1 / (1 + Math.pow(10, question.difficulty - userProgress.user_level));
  const S = wasCorect ? 1 : 0;

  // Ajuste para el usuario
  const newUserLevel = clampLevel(userProgress.user_level + K_USER * (S - P));
  await supabase
    .from('user_progress')
    .update({ user_level: newUserLevel })
    .eq('user_id', userId)
    .eq('subject_id', subjectId);

  // Si la confianza del usuario es alta, actualiza el nivel de la pregunta
  if (userProgress.correct_answers + userProgress.incorrect_answers >= 10) {
    // Ajuste para la pregunta
    const newQuestionLevel = clampLevel(question.difficulty + K_QUESTION * (P - S));
    await supabase
      .from('questions')
      .update({ difficulty: newQuestionLevel })
      .eq('id', questionId);
  }
}

// Client-side function to submit answers
export async function submitAnswerClient(
  questionId: number,
  userId: string,
  response: string,
  wasCorrect: boolean,
  subjectId: number,
  _timeSpentSeconds: number
): Promise<boolean> {
  // Insert the response
  const { error: responseError } = await supabase
    .from('responses')
    .insert({
      user_id: userId,
      question_id: questionId,
      response: parseInt(response), // Convert string to integer (option index)
      was_correct: wasCorrect
    });

  if (responseError) throw responseError;

  // Update or insert user progress
  // Check if user progress exists for this subject
  const { data: progressData, error: progressCheckError } = await supabase
    .from('user_progress')
    .select('*')
    .eq('user_id', userId)
    .eq('subject_id', subjectId)
    .single();

  if (progressCheckError && progressCheckError.code !== 'PGRST116') {
    throw progressCheckError;
  }

  const existingProgress = progressData as Database['public']['Tables']['user_progress']['Row'];

  if (existingProgress) {
    // Update existing record
    const updateData = {
      correct_answers: wasCorrect ? existingProgress.correct_answers + 1 : existingProgress.correct_answers,
      incorrect_answers: wasCorrect ? existingProgress.incorrect_answers : existingProgress.incorrect_answers + 1,
      last_updated: new Date().toISOString()
    };

    const { error: updateError } = await supabase
      .from('user_progress')
      .update(updateData)
      .eq('user_id', userId)
      .eq('subject_id', subjectId);

    if (updateError) throw updateError;
  } else {
    // Insert new record
    const insertData = {
      user_id: userId,
      subject_id: subjectId,
      correct_answers: wasCorrect ? 1 : 0,
      incorrect_answers: wasCorrect ? 0 : 1,
      last_updated: new Date().toISOString()
    };

    const { error: insertError } = await supabase
      .from('user_progress')
      .insert(insertData);

    if (insertError) throw insertError;
  }

  await updateRatings(userId, questionId, subjectId, wasCorrect);

  return true;
}

// Get user progress data for charts
export async function getUserProgressData(userId: string): Promise<ChartDataPoint[]> {
  // Get user responses grouped by date and subject
  const { data: responses, error } = await supabase
    .from('responses')
    .select(`
      created_at,
      was_correct,
      question_id
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: true });

  if (error) throw error;

  // Get questions and subjects separately
  if (responses && responses.length > 0) {
    const questionIds = (responses as Database['public']['Tables']['responses']['Row'][]).map((r: Database['public']['Tables']['responses']['Row']) => r.question_id);

    const { data: questions } = await supabase
      .from('questions')
      .select('id, subject_id')
      .in('id', questionIds);

    const { data: subjects } = await supabase
      .from('subjects')
      .select('id, name');

    // Combine the data
    const enrichedResponses: ProcessedResponse[] = (responses as Database['public']['Tables']['responses']['Row'][]).map((r: Database['public']['Tables']['responses']['Row']) => {
      const question = (questions as any[])?.find((q: any) => q.id === r.question_id);
      const subject = (subjects as any[])?.find((s: any) => s.id === question?.subject_id);

      return {
        created_at: r.created_at,
        was_correct: r.was_correct,
        subject_name: subject?.name?.toLowerCase() || 'desconocido'
      };
    });

    // Process data to create chart format
    const processedData = processResponsesForChart(enrichedResponses);
    return processedData;
  }

  return [];
}

// Get user progress stats for the progress page with date filtering
export async function getUserProgressStats(userId: string, dateRange?: string, specificMonth?: string) {
  try {
    // Calculate date filter
    let dateFilter: string | { start: string; end: string } | null = null;
    if (dateRange && dateRange !== 'all-time') {
      const now = new Date();
      if (dateRange === 'last-week') {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        dateFilter = weekAgo.toISOString();
      } else if (dateRange === 'last-month') {
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        dateFilter = monthAgo.toISOString();
      }
    } else if (specificMonth) {
      // Format: "2024-05" for May 2024
      const [year, month] = specificMonth.split('-');
      const startOfMonth = new Date(parseInt(year), parseInt(month) - 1, 1);
      const endOfMonth = new Date(parseInt(year), parseInt(month), 0, 23, 59, 59);
      dateFilter = { start: startOfMonth.toISOString(), end: endOfMonth.toISOString() };
    }

    // Get user progress
    const { data: progressData, error: progressError } = await supabase
      .from('user_progress')
      .select('*')
      .eq('user_id', userId);

    if (progressError) {
      console.error('Progress stats error:', progressError);
    }

    // Get subjects separately
    const { data: subjects } = await supabase
      .from('subjects')
      .select('*');

    // Combine progress with subjects
    const enrichedProgress: EnrichedProgress[] = (progressData || []).map((p: Database['public']['Tables']['user_progress']['Row']) => {
      const subject = subjects?.find((s: Database['public']['Tables']['subjects']['Row']) => s.id === p.subject_id);
      return {
        ...p,
        subjects: subject
      };
    });

    // Get responses with date filtering
    let responsesQuery = supabase
      .from('responses')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Apply date filter to responses
    if (dateFilter) {
      if (typeof dateFilter === 'string') {
        responsesQuery = responsesQuery.gte('created_at', dateFilter);
      } else {
        responsesQuery = responsesQuery
          .gte('created_at', dateFilter.start)
          .lte('created_at', dateFilter.end);
      }
    }

    const { data: responsesData, error: responsesError } = await responsesQuery;

    if (responsesError) {
      console.error('Responses stats error:', responsesError);
    }

    // Get questions and subjects for responses
    if (responsesData && responsesData.length > 0) {
      const questionIds = (responsesData as Database['public']['Tables']['responses']['Row'][]).map((r: Database['public']['Tables']['responses']['Row']) => r.question_id);

      const { data: questions } = await supabase
        .from('questions')
        .select('*')
        .in('id', questionIds);

      // Combine responses with questions
      const enrichedResponses: EnrichedResponse[] = (responsesData as Database['public']['Tables']['responses']['Row'][]).map((r: Database['public']['Tables']['responses']['Row']) => {
        const question = questions?.find((q: Database['public']['Tables']['questions']['Row']) => q.id === r.question_id);
        return {
          ...r,
          questions: question
        };
      });

      // Filter progress data based on responses if we have date filtering
      let filteredProgressData = enrichedProgress;
      if (dateFilter && enrichedResponses) {
        // Get unique subject IDs from filtered responses
        const subjectIds = Array.from(new Set(enrichedResponses.map((r: EnrichedResponse) => r.questions?.subject_id).filter(Boolean)));
        filteredProgressData = enrichedProgress.filter((p: EnrichedProgress) => subjectIds.includes(p.subject_id));
      }

      return {
        progress: filteredProgressData,
        responses: enrichedResponses
      };
    }

    return {
      progress: enrichedProgress,
      responses: responsesData || []
    };
  } catch (error) {
    console.error('User progress stats error:', error);
    return {
      progress: [],
      responses: []
    };
  }
}

function processResponsesForChart(responses: ProcessedResponse[]): ChartDataPoint[] {
  // Group responses by date
  const dateGroups: DateGroups = {};

  responses.forEach(response => {
    const date = new Date(response.created_at).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit'
    });
    const subjectName = response.subject_name || 'desconocido';

    if (!dateGroups[date]) {
      dateGroups[date] = {};
    }

    if (!dateGroups[date][subjectName]) {
      dateGroups[date][subjectName] = { correct: 0, total: 0 };
    }

    dateGroups[date][subjectName].total++;
    if (response.was_correct) {
      dateGroups[date][subjectName].correct++;
    }
  });

  // Convert to chart format with cumulative percentages
  const chartData: ChartDataPoint[] = [];
  const cumulativeStats: CumulativeStats = {};

  Object.keys(dateGroups).forEach(date => {
    const dayData: ChartDataPoint = { date };

    Object.keys(dateGroups[date]).forEach(subject => {
      if (!cumulativeStats[subject]) {
        cumulativeStats[subject] = { correct: 0, total: 0 };
      }

      cumulativeStats[subject].correct += dateGroups[date][subject].correct;
      cumulativeStats[subject].total += dateGroups[date][subject].total;

      // Calculate percentage
      const percentage = cumulativeStats[subject].total > 0
        ? Math.round((cumulativeStats[subject].correct / cumulativeStats[subject].total) * 100)
        : 0;

      dayData[subject] = percentage;
    });

    chartData.push(dayData);
  });

  return chartData;
}

// Get detailed progress stats by institution and subject
export async function getInstitutionProgressStats(userId: string, dateRange?: string, specificMonth?: string): Promise<InstitutionStats[]> {
  try {
    // Calculate date filter
    let dateFilter: string | { start: string; end: string } | null = null;
    if (dateRange && dateRange !== 'all-time') {
      const now = new Date();
      if (dateRange === 'last-week') {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        dateFilter = weekAgo.toISOString();
      } else if (dateRange === 'last-month') {
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        dateFilter = monthAgo.toISOString();
      }
    } else if (specificMonth) {
      // Format: "2024-05" for May 2024
      const [year, month] = specificMonth.split('-');
      const startOfMonth = new Date(parseInt(year), parseInt(month) - 1, 1);
      const endOfMonth = new Date(parseInt(year), parseInt(month), 0, 23, 59, 59);
      dateFilter = { start: startOfMonth.toISOString(), end: endOfMonth.toISOString() };
    }

    // Get user progress
    const { data: progressData, error: progressError } = await supabase
      .from('user_progress')
      .select('*')
      .eq('user_id', userId);

    if (progressError) {
      console.error('Institution progress stats error:', progressError);
      return [];
    }

    // Get subjects and institutions separately
    const { data: subjects } = await supabase
      .from('subjects')
      .select('*');

    const { data: institutions } = await supabase
      .from('institutions')
      .select('*');

    // Get responses with date filtering for more accurate time calculations
    let responsesQuery = supabase
      .from('responses')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Apply date filter to responses
    if (dateFilter) {
      if (typeof dateFilter === 'string') {
        responsesQuery = responsesQuery.gte('created_at', dateFilter);
      } else {
        responsesQuery = responsesQuery
          .gte('created_at', dateFilter.start)
          .lte('created_at', dateFilter.end);
      }
    }

    const { error: responsesError } = await responsesQuery;

    if (responsesError) {
      console.error('Responses for institution stats error:', responsesError);
    }

    // Group data by institution
    const institutionStatsMap = new Map<string, InternalInstitutionStats>();

    // Process progress data
    progressData?.forEach((progress: Database['public']['Tables']['user_progress']['Row']) => {
      const subject = subjects?.find((s: Database['public']['Tables']['subjects']['Row']) => s.id === progress.subject_id);
      const institution = institutions?.find((i: Database['public']['Tables']['institutions']['Row']) => i.id === subject?.institution_id);

      if (!institution || !subject) return;

      const institutionId = institution.id;

      if (!institutionStatsMap.has(institutionId)) {
        institutionStatsMap.set(institutionId, {
          institution_id: institutionId,
          institution_name: institution.name,
          subjects: new Map(),
          total_answers: 0,
          total_correct: 0,
          total_hours: 0,
          total_level: 0,
          subjects_count: 0,
          average_accuracy: 0,
          average_level: 0
        });
      }

      const institutionStats = institutionStatsMap.get(institutionId);
      if (!institutionStats) return;

      // Add subject data
      if (!institutionStats.subjects.has(subject.id)) {
        institutionStats.subjects.set(subject.id, {
          subject_id: subject.id,
          subject_name: subject.name,
          correct_answers: progress.correct_answers,
          incorrect_answers: progress.incorrect_answers,
          user_level: progress.user_level,
          hours: progress.hours,
          total_answers: progress.correct_answers + progress.incorrect_answers,
          accuracy: progress.correct_answers + progress.incorrect_answers > 0
            ? (progress.correct_answers / (progress.correct_answers + progress.incorrect_answers)) * 100
            : 0
        });
      }

      // Update institution totals
      institutionStats.total_answers += progress.correct_answers + progress.incorrect_answers;
      institutionStats.total_correct += progress.correct_answers;
      institutionStats.total_hours += progress.hours;
      institutionStats.total_level += progress.user_level;
      institutionStats.subjects_count += 1;
    });

    // Calculate averages and convert subjects Map to Array
    const result: InstitutionStats[] = Array.from(institutionStatsMap.values()).map((stats: InternalInstitutionStats) => {
      const average_accuracy = stats.total_answers > 0
        ? Math.round((stats.total_correct / stats.total_answers) * 100)
        : 0;
      const average_level = stats.subjects_count > 0
        ? Math.round((stats.total_level / stats.subjects_count) * 10) / 10
        : 0;
      const subjects = Array.from(stats.subjects.values());

      return {
        institution_id: stats.institution_id,
        institution_name: stats.institution_name,
        subjects,
        total_answers: stats.total_answers,
        total_correct: stats.total_correct,
        total_hours: stats.total_hours,
        total_level: stats.total_level,
        subjects_count: stats.subjects_count,
        average_accuracy,
        average_level
      };
    });

    return result;
  } catch (error) {
    console.error('Error fetching institution progress stats:', error);
    return [];
  }
}