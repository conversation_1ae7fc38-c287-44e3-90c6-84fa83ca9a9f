"use client";

import { motion } from 'framer-motion';
import Image from 'next/image';
import { INSTITUTIONS } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight, GraduationCap } from 'lucide-react';
import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';

export function LandingInstitutions() {
  const t = useTranslations('Institutions');

  return (
    <section id="institutions" className="py-20">
      <div className="container px-4 mx-auto">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">
            {t('title')}{" "}
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              {t('titleHighlight')}
            </span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            {t('description')}
          </p>
        </div>

        <motion.div 
          className="grid md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-8"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.5, staggerChildren: 0.1 }}
          viewport={{ once: true }}
        >
          {INSTITUTIONS.map((institution, index) => (
            <motion.div 
              key={institution.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="overflow-hidden h-full border-none hover:shadow-lg transition-shadow group">
                <div className="relative h-48">
                  <Image
                    src={institution.image}
                    alt={institution.name}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                    <span className="text-white text-2xl font-bold">{institution.shortName}</span>
                  </div>
                </div>
                <CardContent className="p-5">
                  <h3 className="font-semibold text-lg mb-2 line-clamp-1">{institution.name}</h3>
                  <div className="flex items-center justify-between mt-4">
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <GraduationCap className="h-4 w-4 mr-1" />
                      <span>{t('recentExams')}</span>
                    </div>
                    <Link
                      href="/register"
                      className="text-blue-600 dark:text-blue-500 inline-flex items-center text-sm font-medium hover:underline"
                    >
                      {t('explore')}
                      <ArrowRight className="ml-1 h-3 w-3" />
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}