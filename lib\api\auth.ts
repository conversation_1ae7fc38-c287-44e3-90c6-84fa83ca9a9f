// Client-side API functions for authentication
export async function signInAPI(email: string, password: string) {
  const response = await fetch('/api/auth/signin', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password }),
  });

  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(data.error || 'Sign in failed');
  }
  
  return data;
}

export async function signUpAPI(email: string, password: string, metadata: any) {
  const response = await fetch('/api/auth/signup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password, metadata }),
  });

  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(data.error || 'Sign up failed');
  }
  
  return data;
}

export async function signOutAPI() {
  const response = await fetch('/api/auth/signout', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(data.error || 'Sign out failed');
  }
  
  return data;
}

export async function getSessionAPI() {
  const response = await fetch('/api/auth/session', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(data.error || 'Get session failed');
  }
  
  return data.session;
}