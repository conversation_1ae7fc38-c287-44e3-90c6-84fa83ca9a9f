"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON>R<PERSON>, BookOpen, Star } from 'lucide-react';
import { motion } from 'framer-motion';
import { supabase } from '@/lib/supabase/client';
import { useTranslations } from 'next-intl';

interface Subject {
  id: number;
  name: string;
  institution_id: string;
  icon: string;
  description: string;
  color: string;
}

interface SubjectCardProps {
  subject: Subject;
}

interface SubjectProgress {
  correct_answers: number;
  incorrect_answers: number;
  user_level: number;
}

export function SubjectCard({ subject }: SubjectCardProps) {
  const [progress, setProgress] = useState<SubjectProgress | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const t = useTranslations('Dashboard');
  
  useEffect(() => {
    async function fetchProgress() {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session?.user) {
          const { data: progressData } = await supabase
            .from('user_progress')
            .select('correct_answers, incorrect_answers, user_level')
            .eq('user_id', session.user.id)
            .eq('subject_id', subject.id)
            .single();
            
          setProgress(progressData);
        }
      } catch (error) {
        console.error('Error fetching subject progress:', error);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchProgress();
  }, [subject.id]);

  const totalAnswers = progress ? progress.correct_answers + progress.incorrect_answers : 0;
  const correctAnswers = progress ? progress.correct_answers : 0;
  
  return (
    <motion.div 
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="h-full overflow-hidden border-gray-200 dark:border-gray-800 hover:border-blue-200 dark:hover:border-blue-800 transition-colors">
        <CardContent className="p-6">
          <div className={`w-12 h-12 rounded-lg ${subject.color} flex items-center justify-center mb-4`}>
            <BookOpen className="h-6 w-6" />
          </div>
          <h3 className="font-bold text-lg mb-2 line-clamp-1">{subject.name}</h3>
          <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{subject.description}</p>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Preguntas resueltas</span>
              <span className="text-sm text-muted-foreground">
                {isLoading ? '...' : totalAnswers}
              </span>
            </div>
            {totalAnswers > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-xs text-green-600 dark:text-green-400">Correctas</span>
                <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                  {correctAnswers}
                </span>
              </div>
            )}
          </div>
          {progress && (
            <div className="mt-2 space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">{t('accuracy')}</span>
                <span className="text-sm font-medium">
                  {totalAnswers > 0 ? Math.round((progress.correct_answers / totalAnswers) * 100) : 0}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">{t('level')}</span>
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-500 mr-1" />
                  <span className="text-sm font-medium">{progress.user_level?.toFixed(1) || '5.0'}</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="bg-gray-50 dark:bg-gray-900 p-4">
          <Link href={`/questions/${subject.id}`} className="w-full">
            <Button variant="outline" className="w-full">
              <BookOpen className="h-4 w-4 mr-2" />
              Practicar
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
