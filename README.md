# Manyas - Plataforma de Preparación Preuniversitaria

## Ambientes

Este proyecto está configurado para trabajar con múltiples ambientes:

### Desarrollo Local
```bash
# Instalar dependencias
npm install

# Configurar variables de ambiente
cp .env.example .env.local
# Editar .env.local con tus credenciales de desarrollo

# Ejecutar en modo desarrollo
npm run dev
```

### Producción
```bash
# Build para producción
npm run build:prod

# Ejecutar en modo producción
npm run start:prod
```

## Configuración de Ambientes

### Variables de Ambiente

#### Desarrollo (.env.local)
```
NEXT_PUBLIC_SUPABASE_URL=your-dev-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-dev-supabase-anon-key
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development
```

#### Producción (.env.production)
```
NEXT_PUBLIC_SUPABASE_URL=your-prod-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-prod-supabase-anon-key
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production
```

### Supabase - Configuración de Proyectos

1. **Proyecto de Desarrollo**
   - Crear un proyecto en Supabase para desarrollo
   - Configurar las migraciones: `npm run db:migrate:dev`
   - Usar datos de prueba

2. **Proyecto de Producción**
   - Crear un proyecto separado en Supabase para producción
   - Configurar las migraciones: `npm run db:migrate:prod`
   - Usar datos reales

### Scripts Disponibles

- `npm run dev` - Desarrollo local
- `npm run build:dev` - Build para desarrollo
- `npm run build:prod` - Build para producción
- `npm run start:dev` - Ejecutar build de desarrollo
- `npm run start:prod` - Ejecutar build de producción
- `npm run db:migrate:dev` - Migrar base de datos de desarrollo
- `npm run db:migrate:prod` - Migrar base de datos de producción

### Características por Ambiente

#### Desarrollo
- Logging detallado habilitado
- Modo debug activado
- Sin analytics
- Sin reporte de errores

#### Producción
- Logging mínimo (solo errores)
- Analytics habilitado
- Reporte de errores habilitado
- Headers de seguridad configurados

## Deployment

### Vercel (Recomendado)
1. Conectar el repositorio a Vercel
2. Configurar las variables de ambiente de producción
3. Deploy automático en cada push a main

### Otras Plataformas
- Netlify
- Railway
- Heroku

## Estructura del Proyecto

```
├── lib/
│   ├── config/
│   │   └── environment.ts    # Configuración de ambientes
│   ├── utils/
│   │   └── logger.ts         # Sistema de logging
│   └── supabase/
│       └── client.ts         # Cliente de Supabase configurado
├── .env.local               # Variables de desarrollo
├── .env.production          # Variables de producción
└── .env.example            # Ejemplo de variables
```