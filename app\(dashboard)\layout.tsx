"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardSidebar } from '@/components/dashboard/sidebar';
import { DashboardHeader } from '@/components/dashboard/header';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Loader2 } from 'lucide-react';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClientComponentClient();

  useEffect(() => {
    console.log("Layout: Starting auth check...");

    const checkSession = async () => {
      try {
        let { data: { session }, error } = await supabase.auth.getSession();
        console.log("Layout: Initial session check result:", { session: !!session, error });

        if (!session && !error) {
          console.log("Layout: Attempting to refresh session...");
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
          session = refreshData.session;
          if (refreshError) {
            console.error("Layout: Error refreshing session:", refreshError);
          }
        }

        if (error || !session) {
          console.error("Layout: No session found or error occurred:", error);
          router.replace('/login');
          return;
        }

        console.log("Layout: Session found, user authenticated");
        setIsLoading(false);
      } catch (err) {
        console.error("Layout: Error checking session:", err);
        router.replace('/login');
      }
    };

    checkSession();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log("Layout: Auth state changed:", { event, session: !!session });

      if (event === 'SIGNED_OUT' || !session) {
        console.log("Layout: User signed out or no session");
        router.replace('/login');
      } else if (event === 'SIGNED_IN' || session) {
        console.log("Layout: User signed in");
        setIsLoading(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [router, supabase]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="flex min-h-screen">
      <DashboardSidebar />
      <div className="flex flex-col flex-1">
        <DashboardHeader />
        <main className="flex-1 p-6 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
}