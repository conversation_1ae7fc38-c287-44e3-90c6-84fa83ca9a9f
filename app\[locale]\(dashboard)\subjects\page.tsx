"use client";

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Globe } from 'lucide-react';
import { motion } from 'framer-motion';
import { SubjectCard } from '@/components/dashboard/subject-card';
import { getInstitutions, getInstitutionsByCountry, getCountries, getSubjectsWithDefaults } from '@/lib/queries/subjects';

interface Institution {
  id: string;
  name: string;
  country_code?: string;
}

interface Country {
  code: string;
  name: string;
}

interface Subject {
  id: number;
  name: string;
  institution_id: string;
  icon: string;
  description: string;
  color: string;
}

export default function SubjectsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<string>('');
  const [countries, setCountries] = useState<Country[]>([]);
  const [institutions, setInstitutions] = useState<Institution[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const t = useTranslations('Subjects');
  
  useEffect(() => {
    async function fetchInitialData() {
      try {
        // Fetch countries and subjects
        const [countriesData, subjectsData] = await Promise.all([
          getCountries(),
          getSubjectsWithDefaults()
        ]);

        setCountries(countriesData);
        setSubjects(subjectsData);

        // Set default country to first available
        if (countriesData.length > 0) {
          setSelectedCountry(countriesData[0].code);
        }
      } catch (error) {
        console.error('Error fetching initial data:', error);
      } finally {
        setIsLoading(false);
      }
    }
    fetchInitialData();
  }, []);

  // Fetch institutions when country changes
  useEffect(() => {
    async function fetchInstitutions() {
      if (!selectedCountry) return;

      try {
        const institutionsData = await getInstitutionsByCountry(selectedCountry);
        setInstitutions(institutionsData);
      } catch (error) {
        console.error('Error fetching institutions:', error);
        setInstitutions([]);
      }
    }
    fetchInstitutions();
  }, [selectedCountry]);
  
  const getSubjectsByInstitution = (institutionId: string) => {
    return subjects.filter(subject => subject.institution_id === institutionId);
  };

  // Get subjects from institutions of the selected country
  const subjectsFromSelectedCountry = subjects.filter(subject =>
    institutions.some(institution =>
      institution.id === subject.institution_id &&
      (institution.country_code === selectedCountry || !institution.country_code)
    )
  );

  const filteredSubjects = searchTerm
    ? subjectsFromSelectedCountry.filter(subject =>
        subject.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : subjectsFromSelectedCountry;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight mb-1">
          {t('title')}
        </h2>
        <p className="text-muted-foreground">
          {t('description')}
        </p>
      </div>

      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Globe className="h-4 w-4 text-gray-500 dark:text-gray-400" />
          <Select value={selectedCountry} onValueChange={setSelectedCountry}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t('selectCountry')} />
            </SelectTrigger>
            <SelectContent>
              {countries.map((country) => (
                <SelectItem key={country.code} value={country.code}>
                  {country.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
          <Input
            type="search"
            placeholder={t('searchPlaceholder')}
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
      
      {searchTerm ? (
        <div className="space-y-4">
          <h3 className="font-medium">{t('searchResults')}</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredSubjects.map((subject) => (
              <SubjectCard key={subject.id} subject={subject} />
            ))}
          </div>
          {filteredSubjects.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">
                {t('noResults', { searchTerm })}
              </p>
            </div>
          )}
        </div>
      ) : (
        <Tabs defaultValue={institutions[0]?.id.toString()} className="space-y-4">
          <TabsList className="grid grid-cols-2 sm:grid-cols-4 mb-4">
            {institutions.map((institution) => (
              <TabsTrigger 
                key={institution.id}
                value={institution.id.toString()}
                className="text-xs sm:text-sm"
              >
                {institution.name}
              </TabsTrigger>
            ))}
          </TabsList>
          
          {institutions.map((institution) => (
            <TabsContent 
              key={institution.id}
              value={institution.id.toString()}
              className="space-y-4"
            >
              <h3 className="font-medium text-lg">{institution.name}</h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {getSubjectsByInstitution(institution.id).map((subject) => (
                  <SubjectCard key={subject.id} subject={subject} />
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      )}
    </div>
  );
}