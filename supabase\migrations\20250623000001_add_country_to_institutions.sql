/*
  # Add country field to institutions table

  1. Changes
    - Add `country_code` column to `institutions` table
    - Make it a 2-character country code (US, PE)
    - Update existing institutions with default country codes
    
  2. Security
    - No changes to existing RLS policies
*/

-- Add country_code column to institutions table
ALTER TABLE institutions ADD COLUMN IF NOT EXISTS country_code text;

-- Add constraint to ensure country_code is exactly 2 characters
ALTER TABLE institutions ADD CONSTRAINT institutions_country_code_length 
  CHECK (length(country_code) = 2);

-- Update existing institutions with appropriate country codes
-- Peruvian institutions
UPDATE institutions
SET country_code = 'PE'
WHERE name LIKE '%San Marcos%' OR
      name LIKE '%UNMSM%' OR
      name LIKE '%Ingeniería%' OR
      name LIKE '%UNI%' OR
      name LIKE '%Católica%' OR
      name LIKE '%PUCP%' OR
      name LIKE '%Agraria%' OR
      name LIKE '%UNALM%' OR
      name LIKE '%Perú%' OR
      name LIKE '%Lima%';

-- US institutions (if any exist)
UPDATE institutions
SET country_code = 'US'
WHERE name LIKE '%Harvard%' OR
      name LIKE '%MIT%' OR
      name LIKE '%Stanford%' OR
      name LIKE '%Berkeley%' OR
      name LIKE '%University of%' OR
      name LIKE '%College%';

-- Default to PE for any remaining institutions (assuming most are Peruvian)
UPDATE institutions
SET country_code = 'PE'
WHERE country_code IS NULL;

-- Make country_code NOT NULL after setting default values
ALTER TABLE institutions ALTER COLUMN country_code SET NOT NULL;

-- Add index for better performance when filtering by country
CREATE INDEX IF NOT EXISTS idx_institutions_country_code ON institutions(country_code);
