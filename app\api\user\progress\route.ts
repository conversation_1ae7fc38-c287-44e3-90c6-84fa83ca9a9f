import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function PUT(request: NextRequest) {
  try {
    const { userId, subjectId, wasCorrect, timeSpentSeconds } = await request.json();
    
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Verify user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session?.user || session.user.id !== userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // First, try to get existing progress
    const { data: existingProgress, error: selectError } = await supabase
      .from('user_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('subject_id', subjectId)
      .single();

    if (selectError && selectError.code !== 'PGRST116') { // PGRST116 is "not found" error
      return NextResponse.json({ error: selectError.message }, { status: 400 });
    }

    const timeSpentHours = timeSpentSeconds / 3600; // Convert seconds to hours

    if (existingProgress) {
      // Update existing record
      const updateData = {
        correct_answers: wasCorrect
          ? existingProgress.correct_answers + 1
          : existingProgress.correct_answers,
        incorrect_answers: wasCorrect
          ? existingProgress.incorrect_answers
          : existingProgress.incorrect_answers + 1,
        hours: existingProgress.hours + timeSpentHours,
        last_updated: new Date().toISOString()
      };

      const { error: updateError } = await supabase
        .from('user_progress')
        .update(updateData)
        .eq('user_id', userId)
        .eq('subject_id', subjectId);

      if (updateError) {
        return NextResponse.json({ error: updateError.message }, { status: 400 });
      }
    } else {
      // Insert new record
      const insertData = {
        user_id: userId,
        subject_id: subjectId,
        correct_answers: wasCorrect ? 1 : 0,
        incorrect_answers: wasCorrect ? 0 : 1,
        user_level: 5, // Default level as requested
        hours: timeSpentHours,
        last_updated: new Date().toISOString()
      };

      const { error: insertError } = await supabase
        .from('user_progress')
        .insert(insertData);

      if (insertError) {
        return NextResponse.json({ error: insertError.message }, { status: 400 });
      }
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}