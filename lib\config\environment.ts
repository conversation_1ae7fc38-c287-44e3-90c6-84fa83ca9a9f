// Configuración de ambientes
export const config = {
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  appEnv: process.env.NEXT_PUBLIC_APP_ENV || 'development',
  
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  },
  
  // URLs base según el ambiente
  baseUrl: process.env.NEXT_PUBLIC_APP_ENV === 'production' 
    ? 'https://tu-dominio-produccion.com'
    : 'http://localhost:3000',
    
  // Configuraciones específicas por ambiente
  features: {
    enableAnalytics: process.env.NEXT_PUBLIC_APP_ENV === 'production',
    enableDebugMode: process.env.NEXT_PUBLIC_APP_ENV === 'development',
    enableErrorReporting: process.env.NEXT_PUBLIC_APP_ENV === 'production',
  },
  
  // Configuración de logging
  logging: {
    level: process.env.NEXT_PUBLIC_APP_ENV === 'production' ? 'error' : 'debug',
    enableConsole: process.env.NEXT_PUBLIC_APP_ENV === 'development',
  }
};

// Validar que las variables requeridas estén presentes
if (!config.supabase.url) {
  throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');
}

if (!config.supabase.anonKey) {
  throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY is required');
}