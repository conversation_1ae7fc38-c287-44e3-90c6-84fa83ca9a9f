"use client";

import { motion } from 'framer-motion';
import { 
  BookOpen, 
  GraduationCap, 
  LineChart, 
  CheckCircle2, 
  Lightbulb,
  Clock,
  Award,
  Smartphone
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslations } from 'next-intl';



const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};

export function LandingFeatures() {
  const t = useTranslations('Features');

  const features = [
    {
      icon: <BookOpen className="h-10 w-10 text-blue-600" />,
      title: t('feature1Title'),
      description: t('feature1Description')
    },
    {
      icon: <LineChart className="h-10 w-10 text-purple-600" />,
      title: t('feature2Title'),
      description: t('feature2Description')
    },
    {
      icon: <GraduationCap className="h-10 w-10 text-green-600" />,
      title: t('feature3Title'),
      description: t('feature3Description')
    },
    {
      icon: <CheckCircle2 className="h-10 w-10 text-amber-600" />,
      title: t('feature4Title'),
      description: t('feature4Description')
    },
    {
      icon: <Lightbulb className="h-10 w-10 text-red-600" />,
      title: t('feature5Title'),
      description: t('feature5Description')
    },
    {
      icon: <Smartphone className="h-10 w-10 text-indigo-600" />,
      title: t('feature6Title'),
      description: t('feature6Description')
    }
  ];

  return (
    <section id="features" className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="container px-4 mx-auto">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">
            {t('title')}{" "}
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              {t('titleHighlight')}
            </span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            {t('description')}
          </p>
        </div>

        <motion.div 
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
        >
          {features.map((feature, index) => (
            <motion.div key={index} variants={item}>
              <Card className="h-full border border-gray-200 dark:border-gray-800 hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="mb-4">{feature.icon}</div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}