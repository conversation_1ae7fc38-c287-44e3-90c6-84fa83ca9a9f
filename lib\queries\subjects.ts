// Client-side queries for reading subjects data
import { supabase } from '@/lib/supabase/client';

interface Institution {
  id: string;
  name: string;
}

interface Subject {
  id: number;
  name: string;
  institution_id: string;
}

export async function getInstitutions(): Promise<Institution[]> {
  const { data, error } = await supabase
    .from('institutions')
    .select('*');
    
  if (error) throw error;
  return data || [];
}

export async function getSubjects(): Promise<Subject[]> {
  const { data, error } = await supabase
    .from('subjects')
    .select('*');
    
  if (error) throw error;
  return data || [];
}

export async function getSubjectsWithDefaults() {
  const subjects = await getSubjects();
  
  // Add default icon and color for each subject
  return subjects.map((subject) => ({
    ...subject,
    icon: 'BookOpen',
    color: 'bg-blue-100 dark:bg-blue-950',
    description: 'Preparación completa para el examen de admisión'
  }));
}