// Client-side queries for reading subjects data
import { supabase } from '@/lib/supabase/client';
import { Database } from '@/lib/supabase/database.types';

interface Institution {
  id: string;
  name: string;
  country_code?: string;
}

interface Subject {
  id: number;
  name: string;
  institution_id: string;
}

export async function getInstitutions(): Promise<Institution[]> {
  const { data, error } = await supabase
    .from('institutions')
    .select('*')
    .order('name');

  if (error) throw error;
  return data || [];
}

export async function getInstitutionsByCountry(countryCode: string): Promise<Institution[]> {
  try {
    // First try to use country_code column
    const { data, error } = await supabase
      .from('institutions')
      .select('*')
      .eq('country_code', countryCode)
      .order('name');

    if (!error && data) {
      return data.map((inst: Database['public']['Tables']['institutions']['Row']) => ({ ...inst, country_code: countryCode }));
    }

    // If country_code column doesn't exist, filter by institution names
    const { data: allInstitutions, error: allError } = await supabase
      .from('institutions')
      .select('*')
      .order('name');

    if (allError) throw allError;

    // Filter institutions based on country and name patterns
    const filteredInstitutions = (allInstitutions || []).filter(institution => {
      const name = institution.name.toLowerCase();

      if (countryCode === 'PE') {
        // Peruvian institutions
        return name.includes('san marcos') ||
          name.includes('unmsm') ||
          name.includes('ingeniería') ||
          name.includes('uni') ||
          name.includes('católica') ||
          name.includes('pucp') ||
          name.includes('agraria') ||
          name.includes('unalm') ||
          name.includes('perú') ||
          name.includes('lima') ||
          name.includes('nacional');
      } else if (countryCode === 'US') {
        // US institutions
        return name.includes('harvard') ||
          name.includes('mit') ||
          name.includes('stanford') ||
          name.includes('berkeley') ||
          name.includes('university of') ||
          name.includes('college') ||
          name.includes('institute of technology');
      }

      return false;
    });

    // Add country_code to the results
    return filteredInstitutions.map((inst: Database['public']['Tables']['institutions']['Row']) => ({
      ...inst,
      country_code: countryCode
    }));

  } catch (error) {
    console.error('Error fetching institutions by country:', error);
    return [];
  }
}

export async function getCountries(): Promise<{ code: string, name: string }[]> {
  try {
    const { data, error } = await supabase
      .from('institutions')
      .select('country_code')
      .order('country_code');

    if (error) {
      // If country_code column doesn't exist yet, return default countries
      console.log('Country code column not found, using defaults');
      return [
        { code: 'PE', name: 'Perú' },
        { code: 'US', name: 'Estados Unidos' }
      ];
    }

    // Get unique country codes and map to country names
    const uniqueCountries = data?.map(item => item.country_code);

    const countryNames: { [key: string]: string } = {
      'US': 'Estados Unidos',
      'PE': 'Perú'
    };

    return uniqueCountries.length > 0
      ? uniqueCountries.map(code => ({
        code,
        name: countryNames[code] || code
      }))
      : [
        { code: 'PE', name: 'Perú' },
        { code: 'US', name: 'Estados Unidos' }
      ];
  } catch (error) {
    console.error('Error fetching countries:', error);
    // Return default countries as fallback
    return [
      { code: 'PE', name: 'Perú' },
      { code: 'US', name: 'Estados Unidos' }
    ];
  }
}

export async function getSubjects(): Promise<Subject[]> {
  const { data, error } = await supabase
    .from('subjects')
    .select('*');

  if (error) throw error;
  return data || [];
}

export async function getSubjectsWithDefaults() {
  const subjects = await getSubjects();

  // Add default icon and color for each subject
  return subjects.map((subject) => ({
    ...subject,
    icon: 'BookOpen',
    color: 'bg-blue-100 dark:bg-blue-950',
    description: 'Preparación completa para el examen de admisión'
  }));
}