"use client";

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowRight, 
  BookOpen, 
  CheckCircle, 
  Clock, 
  LineChart, 
  ThumbsUp, 
  Zap,
  Award,
  BookMarked, Star
} from 'lucide-react';
import { supabase } from '@/lib/supabase/client';
import { getInstitutionProgressStats } from '@/lib/queries/questions';
import { RecentActivityCard } from '@/components/dashboard/recent-activity-card';
import { PerformanceChart } from '@/components/dashboard/performance-chart';

interface UserData {
  name: string;
  subscription_status: string;
}

interface Subject {
  id: number;
  name: string;
  institution_id: string;
}

interface Institution {
  id: string;
  name: string;
}

interface UserProgress {
  subject_id: number;
  correct_answers: number;
  incorrect_answers: number;
  user_level: number;
  hours: number;
  last_updated: string;
  subject?: Subject;
  institution?: Institution;
}

interface Question {
  id: number;
  subject_id: number;
  subject?: Subject;
}

interface Response {
  id: number;
  question_id: number;
  response: string;
  was_correct: boolean;
  created_at: string;
  question?: Question;
}

interface SubjectStats {
  subject_id: number;
  subject_name: string;
  correct_answers: number;
  incorrect_answers: number;
  user_level: number;
  hours: number;
  total_answers: number;
  accuracy: number;
}

interface InstitutionStats {
  institution_id: string;
  institution_name: string;
  subjects: SubjectStats[];
  total_answers: number;
  total_correct: number;
  total_hours: number;
  total_level: number;
  subjects_count: number;
  average_accuracy: number;
  average_level: number;
}

export default function DashboardPage() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userProgress, setUserProgress] = useState<UserProgress[]>([]);
  const [recentResponses, setRecentResponses] = useState<Response[]>([]);
  const [institutionStats, setInstitutionStats] = useState<InstitutionStats[]>([]);
  const [userId, setUserId] = useState<string | null>(null);
  const t = useTranslations('Dashboard');
  
  useEffect(() => {
    async function fetchData() {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session?.user) {
          setUserId(session.user.id);

          // Set user data from session
          const userData = {
            name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || t('user'),
            subscription_status: session.user.user_metadata?.subscription_status || 'inactive'
          };

          setUserData(userData);

          // Fetch progress data with subject and institution names
          try {
            const { data: progress, error: progressError } = await supabase
              .from('user_progress')
              .select('*')
              .eq('user_id', session.user.id)
              .order('last_updated', { ascending: false });

            if (progressError) {
              console.error('Progress error:', progressError);
              setUserProgress([]);
            } else if (progress) {
              // Fetch subjects and institutions separately
              const subjectIds = progress.map(p => p.subject_id);
              
              const { data: subjects, error: subjectsError } = await supabase
                .from('subjects')
                .select('*')
                .in('id', subjectIds);

              const { data: institutions, error: institutionsError } = await supabase
                .from('institutions')
                .select('*');

              if (subjectsError) {
                console.error('Subjects error:', subjectsError);
              }
              if (institutionsError) {
                console.error('Institutions error:', institutionsError);
              }

              // Combine the data
              const enrichedProgress: UserProgress[] = progress.map(p => {
                const subject = subjects?.find(s => s.id === p.subject_id);
                const institution = institutions?.find(i => i.id === subject?.institution_id);
                
                return {
                  ...p,
                  subject,
                  institution
                };
              });

              setUserProgress(enrichedProgress);

              // Get detailed institution statistics using the new function
              const detailedInstitutionStats = await getInstitutionProgressStats(session.user.id);
              setInstitutionStats(detailedInstitutionStats);
            }
          } catch (progressErr) {
            console.error('Progress fetch error:', progressErr);
            setUserProgress([]);
            setInstitutionStats([]);
          }

          // Fetch responses data with institution information
          try {
            const { data: responses, error: responsesError } = await supabase
              .from('responses')
              .select('*')
              .eq('user_id', session.user.id)
              .order('created_at', { ascending: false })
              .limit(10);

            if (responsesError) {
              console.error('Responses error:', responsesError);
              setRecentResponses([]);
            } else if (responses) {
              // Fetch questions and subjects separately
              const questionIds = responses.map(r => r.question_id);
              
              const { data: questions, error: questionsError } = await supabase
                .from('questions')
                .select('*')
                .in('id', questionIds);

              const { data: subjects, error: subjectsError } = await supabase
                .from('subjects')
                .select('*');

              const { data: institutions, error: institutionsError } = await supabase
                .from('institutions')
                .select('*');

              if (questionsError) {
                console.error('Questions error:', questionsError);
              }
              if (subjectsError) {
                console.error('Subjects error:', subjectsError);
              }
              if (institutionsError) {
                console.error('Institutions error:', institutionsError);
              }

              // Combine the data
              const enrichedResponses: Response[] = responses.map(r => {
                const question = questions?.find(q => q.id === r.question_id);
                const subject = subjects?.find(s => s.id === question?.subject_id);
                const institution = institutions?.find(i => i.id === subject?.institution_id);
                
                return {
                  ...r,
                  question: question ? {
                    ...question,
                    subject: subject ? {
                      ...subject,
                      institution_id: subject.institution_id
                    } : undefined
                  } : undefined
                };
              });

              setRecentResponses(enrichedResponses);
            }
          } catch (responsesErr) {
            console.error('Responses fetch error:', responsesErr);
            setRecentResponses([]);
          }
        } else {
          window.location.href = '/login';
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [t]);

  // Calculate overall statistics from real data
  const totalAnswers = userProgress.reduce((sum, p) => sum + p.correct_answers + p.incorrect_answers, 0);
  const totalCorrect = userProgress.reduce((sum, p) => sum + p.correct_answers, 0);
  const totalHours = userProgress.reduce((sum, p) => sum + p.hours, 0);

  // Get unique institutions count
  const uniqueInstitutions = new Set(
    userProgress
      .map(p => p.institution?.id)
      .filter((id): id is string => id !== undefined && id !== null)
  ).size;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {t('welcome', { name: userData?.name || t('student') })}
          </h2>
          <p className="text-muted-foreground">
            {t('welcomeDescription')}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Link href="/subjects">
            <Button className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              {t('exploreSubjects')}
            </Button>
          </Link>
        </div>
      </div>

      {userData?.subscription_status !== 'active' && (
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 border-blue-100 dark:border-blue-900">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-500" />
                  <h3 className="font-medium text-lg">{t('unlockContent')}</h3>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 md:max-w-md">
                  {t('upgradeDescription')}
                </p>
              </div>
              <Link href="/subscription">
                <Button>{t('viewPlans')}</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4 md:gap-8 grid-cols-1 md:grid-cols-4 lg:grid-cols-6">
        <div className="col-span-1 md:col-span-3 lg:col-span-4 space-y-4 md:space-y-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{t('generalProgress')}</CardTitle>
              <CardDescription>
                {t('generalProgressDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <PerformanceChart userId={userId || undefined} />
              </div>
            </CardContent>
          </Card>
          
          {/* Institution Statistics */}
          {institutionStats.length > 0 && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">{t('progressByUniversity')}</CardTitle>
                <CardDescription>
                  {t('progressByUniversityDescription')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {institutionStats.map((stats) => (
                    <div key={stats.institution_id} className="p-6 border rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="text-lg font-semibold">{stats.institution_name}</h4>
                        <span className="text-sm text-muted-foreground bg-white dark:bg-gray-700 px-2 py-1 rounded">
                          {stats.subjects_count} {stats.subjects_count === 1 ? t('subjectSingular') : t('subjectsPlural')}
                        </span>
                      </div>

                      {/* Institution Summary */}
                      <div className="grid grid-cols-4 gap-4 mb-4 text-sm">
                        <div className="text-center p-3 bg-white dark:bg-gray-700 rounded">
                          <span className="text-muted-foreground block">{t('questionsLabel')}</span>
                          <p className="font-bold text-lg">{stats.total_answers}</p>
                        </div>
                        <div className="text-center p-3 bg-white dark:bg-gray-700 rounded">
                          <span className="text-muted-foreground block">{t('accuracyLabel')}</span>
                          <p className="font-bold text-lg text-green-600">{stats.average_accuracy}%</p>
                        </div>
                        <div className="text-center p-3 bg-white dark:bg-gray-700 rounded">
                          <span className="text-muted-foreground block">{t('hours')}</span>
                          <p className="font-bold text-lg">{Math.round(stats.total_hours * 10) / 10}h</p>
                        </div>
                        <div className="text-center p-3 bg-white dark:bg-gray-700 rounded">
                          <span className="text-muted-foreground block">{t('level')}</span>
                          <div className="flex items-center justify-center">
                            <Star className="h-4 w-4 text-yellow-500 mr-1" />
                            <p className="font-bold text-lg">{stats.average_level}</p>
                          </div>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      {stats.total_answers > 0 && (
                        <div className="mb-4">
                          <div className="flex justify-between text-sm mb-1">
                            <span>{t('overallProgress')}</span>
                            <span>{stats.average_accuracy}%</span>
                          </div>
                          <Progress value={stats.average_accuracy} className="h-2" />
                        </div>
                      )}

                      {/* Subjects Detail */}
                      <div className="space-y-2">
                        <h5 className="font-medium text-sm text-muted-foreground">{t('subjectDetails')}</h5>
                        <div className="grid gap-2">
                          {stats.subjects.map((subject) => (
                            <div key={subject.subject_id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-700 rounded text-sm">
                              <div className="flex-1">
                                <p className="font-medium">{subject.subject_name}</p>
                                <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                                  <span>{subject.total_answers} {t('questionsLabel')}</span>
                                  <span>{Math.round(subject.accuracy)}% {t('accuracy')}</span>
                                  <span>{subject.hours.toFixed(1)}h</span>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="flex items-center">
                                  <Star className="h-3 w-3 text-yellow-500 mr-1" />
                                  <span className="font-medium">{subject.user_level.toFixed(1)}</span>
                                </div>
                                <div className="w-16">
                                  <Progress value={subject.accuracy} className="h-1" />
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
          
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">{t('recentSubjects')}</CardTitle>
                <CardDescription>
                  {t('continueWhere')}
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-4">
                  {userProgress.length > 0 ? (
                    userProgress.slice(0, 3).map((progress) => {
                      const totalAnswers = progress.correct_answers + progress.incorrect_answers;
                      const accuracyPercent = totalAnswers > 0 
                        ? Math.round((progress.correct_answers / totalAnswers) * 100)
                        : 0;

                      return (
                        <div key={progress.subject_id} className="flex items-center gap-4">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center bg-blue-100 dark:bg-blue-950`}>
                            <BookMarked className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate">{progress.subject?.name || t('subjectSingular')}</p>
                            <p className="text-xs text-muted-foreground truncate">
                              {progress.institution?.name || t('university')}
                            </p>
                            <div className="flex items-center justify-between mt-1">
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <span>{t('questionsCount', { count: totalAnswers })}</span>
                                {totalAnswers > 0 && (
                                  <>
                                    <span>•</span>
                                    <span className="text-green-600 dark:text-green-400">
                                      {t('correctPercentage', { percentage: accuracyPercent })}
                                    </span>
                                  </>
                                )}
                              </div>
                            </div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {t('lastActivity', { date: new Date(progress.last_updated).toLocaleDateString() })}
                            </div>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
                      <p className="text-sm">{t('noSubjectsPracticed')}</p>
                      <p className="text-xs">{t('startAnswering')}</p>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="pt-2">
                <Link href="/subjects" className="w-full">
                  <Button variant="outline" className="w-full text-sm" size="sm">
                    {t('viewAllSubjects')}
                  </Button>
                </Link>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">{t('generalStats')}</CardTitle>
                <CardDescription>
                  {t('generalStatsDescription')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-muted-foreground">{t('answered')}</span>
                    </div>
                    <p className="text-2xl font-bold">{totalAnswers}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <ThumbsUp className="h-4 w-4 text-blue-500" />
                      <span className="text-sm text-muted-foreground">{t('correct')}</span>
                    </div>
                    <p className="text-2xl font-bold">{totalCorrect}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-amber-500" />
                      <span className="text-sm text-muted-foreground">{t('hours')}</span>
                    </div>
                    <p className="text-2xl font-bold">{Math.round(totalHours * 10) / 10}</p>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <LineChart className="h-4 w-4 text-purple-500" />
                      <span className="text-sm text-muted-foreground">{t('universities')}</span>
                    </div>
                    <p className="text-2xl font-bold">{uniqueInstitutions}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        <div className="col-span-1 md:col-span-1 lg:col-span-2">
          <Card className="h-full">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{t('recentActivityTitle')}</CardTitle>
                <Button variant="ghost" size="sm" className="h-8 gap-1">
                  <span className="sr-only sm:not-sr-only text-xs">{t('viewAll')}</span>
                  <ArrowRight className="h-3.5 w-3.5" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="all">
                <TabsList className="mb-4">
                  <TabsTrigger value="all" className="text-xs">{t('allTab')}</TabsTrigger>
                  <TabsTrigger value="correct" className="text-xs">{t('correctTab')}</TabsTrigger>
                  <TabsTrigger value="incorrect" className="text-xs">{t('incorrectTab')}</TabsTrigger>
                </TabsList>
                
                <TabsContent value="all" className="m-0">
                  <div className="space-y-4">
                    {recentResponses.length > 0 ? (
                      recentResponses.slice(0, 5).map((response) => (
                        <RecentActivityCard
                          key={response.id}
                          title={`${response.question?.subject?.name || t('unknownSubject')}`}
                          description={`${response.question?.subject?.institution_id || t('university')} - ${response.was_correct ? t('answeredCorrectly') : t('answeredIncorrectly')}`}
                          timestamp={new Date(response.created_at).toLocaleString('es-ES')}
                          isCorrect={response.was_correct}
                        />
                      ))
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <CheckCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">{t('noRecentActivity')}</p>
                        <p className="text-xs">{t('answerQuestions')}</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
                
                <TabsContent value="correct" className="m-0">
                  <div className="space-y-4">
                    {recentResponses
                      .filter(r => r.was_correct)
                      .slice(0, 5)
                      .map((response) => (
                        <RecentActivityCard
                          key={response.id}
                          title={`${response.question?.subject?.name || t('unknownSubject')}`}
                          description={`${response.question?.subject?.institution_id || t('university')} - ${t('answeredCorrectly')}`}
                          timestamp={new Date(response.created_at).toLocaleString('es-ES')}
                          isCorrect={true}
                        />
                      ))}
                    {recentResponses.filter(r => r.was_correct).length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        <CheckCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">{t('noRecentCorrect')}</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
                
                <TabsContent value="incorrect" className="m-0">
                  <div className="space-y-4">
                    {recentResponses
                      .filter(r => !r.was_correct)
                      .slice(0, 5)
                      .map((response) => (
                        <RecentActivityCard
                          key={response.id}
                          title={`${response.question?.subject?.name || t('unknownSubject')}`}
                          description={`${response.question?.subject?.institution_id || t('university')} - ${t('answeredIncorrectly')}`}
                          timestamp={new Date(response.created_at).toLocaleString('es-ES')}
                          isCorrect={false}
                        />
                      ))}
                    {recentResponses.filter(r => !r.was_correct).length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        <CheckCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">{t('noRecentIncorrect')}</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{t('achievementsTitle')}</CardTitle>
          <CardDescription>
            {t('achievementsSubtitle')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
            <div className={`flex flex-col items-center justify-center p-4 border rounded-lg ${
              totalAnswers > 0 
                ? 'border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/30' 
                : 'border-gray-200 dark:border-gray-800 opacity-60'
            }`}>
              <div className={`p-3 rounded-full mb-3 ${
                totalAnswers > 0 
                  ? 'bg-amber-100 dark:bg-amber-900/50' 
                  : 'bg-gray-100 dark:bg-gray-800'
              }`}>
                <Award className={`h-6 w-6 ${
                  totalAnswers > 0 
                    ? 'text-amber-500' 
                    : 'text-gray-400'
                }`} />
              </div>
              <p className="font-medium text-center">{t('firstStep')}</p>
              <p className="text-xs text-muted-foreground text-center">{t('firstStepDescription')}</p>
              {totalAnswers > 0 && (
                <div className="mt-2 text-xs text-amber-600 dark:text-amber-400 font-medium">
                  {t('completed')}
                </div>
              )}
            </div>
            <div className="flex flex-col items-center justify-center p-4 border border-gray-200 dark:border-gray-800 rounded-lg opacity-60">
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-full mb-3">
                <Clock className="h-6 w-6 text-gray-400" />
              </div>
              <p className="font-medium text-center">{t('discipline')}</p>
              <p className="text-xs text-muted-foreground text-center">{t('disciplineDescription')}</p>
            </div>
            <div className={`flex flex-col items-center justify-center p-4 border rounded-lg ${
              uniqueInstitutions >= 2 
                ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/30' 
                : 'border-gray-200 dark:border-gray-800 opacity-60'
            }`}>
              <div className={`p-3 rounded-full mb-3 ${
                uniqueInstitutions >= 2 
                  ? 'bg-blue-100 dark:bg-blue-900/50' 
                  : 'bg-gray-100 dark:bg-gray-800'
              }`}>
                <BookOpen className={`h-6 w-6 ${
                  uniqueInstitutions >= 2 
                    ? 'text-blue-500' 
                    : 'text-gray-400'
                }`} />
              </div>
              <p className="font-medium text-center">{t('explorerTitle')}</p>
              <p className="text-xs text-muted-foreground text-center">{t('explorerDesc')}</p>
              {uniqueInstitutions >= 2 && (
                <div className="mt-2 text-xs text-blue-600 dark:text-blue-400 font-medium">
                  {t('completed')}
                </div>
              )}
            </div>
            <div className="flex flex-col items-center justify-center p-4 border border-gray-200 dark:border-gray-800 rounded-lg opacity-60">
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-full mb-3">
                <Zap className="h-6 w-6 text-gray-400" />
              </div>
              <p className="font-medium text-center">{t('genius')}</p>
              <p className="text-xs text-muted-foreground text-center">{t('geniusDescription')}</p>
            </div>
            <div className="flex flex-col items-center justify-center p-4 border border-gray-200 dark:border-gray-800 rounded-lg opacity-60">
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-full mb-3">
                <LineChart className="h-6 w-6 text-gray-400" />
              </div>
              <p className="font-medium text-center">{t('advanced')}</p>
              <p className="text-xs text-muted-foreground text-center">{t('advancedDescription')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
