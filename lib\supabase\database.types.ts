export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      institutions: {
        Row: {
          id: string
          name: string
        }
        Insert: {
          id?: string
          name: string
        }
        Update: {
          id?: string
          name?: string
        }
        Relationships: []
      }
      questions: {
        Row: {
          id: number
          question: string
          options: string[]
          correct_answer: number
          solution: string
          image: string | null
          question_image_url: string | null
          solution_image_url: string | null
          subject_id: number
          difficulty: number
          source_id: string | null
          topic_id: string | null
          created_at: string
        }
        Insert: {
          id?: number
          question: string
          options: string[]
          correct_answer: number
          solution: string
          image?: string | null
          question_image_url?: string | null
          solution_image_url?: string | null
          subject_id: number
          difficulty: number
          source_id?: string | null
          topic_id?: string | null
          created_at?: string
        }
        Update: {
          id?: number
          question?: string
          options?: string[]
          correct_answer?: number
          solution?: string
          image?: string | null
          question_image_url?: string | null
          solution_image_url?: string | null
          subject_id?: number
          difficulty?: number
          source_id?: string | null
          topic_id?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "questions_subject_id_fkey"
            columns: ["subject_id"]
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "questions_source_id_fkey"
            columns: ["source_id"]
            referencedRelation: "sources"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "questions_topic_id_fkey"
            columns: ["topic_id"]
            referencedRelation: "topics"
            referencedColumns: ["id"]
          }
        ]
      }
      responses: {
        Row: {
          id: number
          user_id: string
          question_id: number
          response: number
          was_correct: boolean
          created_at: string
        }
        Insert: {
          id?: number
          user_id: string
          question_id: number
          response: number
          was_correct: boolean
          created_at?: string
        }
        Update: {
          id?: number
          user_id?: string
          question_id?: number
          response?: number
          was_correct?: boolean
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "responses_question_id_fkey"
            columns: ["question_id"]
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "responses_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      sources: {
        Row: {
          id: string
          name: string
          date: string
          institution_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          date: string
          institution_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          date?: string
          institution_id?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "sources_institution_id_fkey"
            columns: ["institution_id"]
            referencedRelation: "institutions"
            referencedColumns: ["id"]
          }
        ]
      }
      subjects: {
        Row: {
          id: number
          name: string
          institution_id: string
        }
        Insert: {
          id?: number
          name: string
          institution_id: string
        }
        Update: {
          id?: number
          name?: string
          institution_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subjects_institution_id_fkey"
            columns: ["institution_id"]
            referencedRelation: "institutions"
            referencedColumns: ["id"]
          }
        ]
      }
      topics: {
        Row: {
          id: string
          name: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          created_at?: string
        }
        Relationships: []
      }
      user_progress: {
        Row: {
          id: number
          user_id: string
          subject_id: number
          correct_answers: number
          incorrect_answers: number
          last_updated: string
          user_level: number
          hours: number
        }
        Insert: {
          id?: number
          user_id: string
          subject_id: number
          correct_answers?: number
          incorrect_answers?: number
          last_updated?: string
          user_level?: number
          hours?: number
        }
        Update: {
          id?: number
          user_id?: string
          subject_id?: number
          correct_answers?: number
          incorrect_answers?: number
          last_updated?: string
          user_level?: number
          hours?: number
        }
        Relationships: [
          {
            foreignKeyName: "user_progress_subject_id_fkey"
            columns: ["subject_id"]
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_progress_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          name: string
          email: string
          subscription_status: string
          subscription_expires: string | null
          created_at: string
        }
        Insert: {
          id: string
          name: string
          email: string
          subscription_status?: string
          subscription_expires?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string
          subscription_status?: string
          subscription_expires?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "users_id_fkey"
            columns: ["id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}