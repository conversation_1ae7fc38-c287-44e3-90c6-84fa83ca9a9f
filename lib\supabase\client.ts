import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { config } from '@/lib/config/environment';
import { Database } from './database.types';

// Create a singleton instance to ensure consistent session management
let supabaseClient: ReturnType<typeof createClientComponentClient<Database>> | null = null;

export const supabase = (() => {
  // Only initialize on client-side
  if (typeof window === 'undefined') {
    return null as any;
  }
  
  if (!supabaseClient) {
    supabaseClient = createClientComponentClient({
      supabaseUrl: config.supabase.url,
      supabaseKey: config.supabase.anonKey,
    });
  }
  return supabaseClient;
})();

// Helper function to get session with error handling
export const getSession = async () => {
  // Only run on client-side
  if (typeof window === 'undefined') {
    return null;
  }
  
  try {
    const client = supabase;
    if (!client) return null;
    
    const { data: { session }, error } = await client.auth.getSession();
    if (error) {
      if (config.logging.enableConsole) {
        console.error('Error getting session:', error);
      }
      return null;
    }
    return session;
  } catch (error) {
    if (config.logging.enableConsole) {
      console.error('Error in getSession:', error);
    }
    return null;
  }
};