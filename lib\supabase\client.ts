import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { config } from '@/lib/config/environment';

// Create a singleton instance to ensure consistent session management
let supabaseClient: ReturnType<typeof createClientComponentClient> | null = null;

export const supabase = (() => {
  if (!supabaseClient) {
    supabaseClient = createClientComponentClient({
      supabaseUrl: config.supabase.url,
      supabaseKey: config.supabase.anonKey,
    });
  }
  return supabaseClient;
})();

// Helper function to get session with error handling
export const getSession = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      if (config.logging.enableConsole) {
        console.error('Error getting session:', error);
      }
      return null;
    }
    return session;
  } catch (error) {
    if (config.logging.enableConsole) {
      console.error('Error in getSession:', error);
    }
    return null;
  }
};

