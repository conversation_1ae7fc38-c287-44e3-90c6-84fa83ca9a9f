{"HomePage": {"title": "Prepare for your university admission exams with <PERSON><PERSON>", "description": "Manyas is a platform designed specifically for Peruvian students preparing for university admission exams. Study efficiently, perform at your best."}, "Metadata": {"title": "Manyas - University Preparation", "description": "Manyas is a platform designed specifically for Peruvian students preparing for university admission exams. Study efficiently, perform at your best."}, "Navbar": {"features": "Features", "universities": "Universities", "pricing": "Pricing", "login": "Sign In", "register": "Sign Up"}, "Dashboard": {"title": "Dashboard", "subjects": "Subjects", "progress": "Progress", "subscription": "Subscription", "profile": "Profile", "search": "Search...", "notifications": "Notifications", "upgradeplan": "Upgrade Plan", "welcome": "Welcome, {name}!", "welcomeDescription": "Here's a summary of your progress and recent activities.", "exploreSubjects": "Explore Subjects", "generalProgress": "Your overall progress", "generalProgressDescription": "Summary of your performance across all subjects", "generalStats": "General Statistics", "generalStatsDescription": "Your performance so far", "answered": "Answered", "correct": "Correct", "hours": "Hours", "accuracy": "Accuracy", "level": "Level", "universities": "Universities", "recentActivity": "Recent Activity", "recentActivityDescription": "Your latest responses and progress", "allActivity": "All Activity", "correctAnswers": "Correct Answers", "incorrectAnswers": "Incorrect Answers", "noRecentCorrect": "No recent correct answers", "noRecentIncorrect": "No recent incorrect answers", "achievements": "Achievements", "achievementsDescription": "Keep earning achievements and improve your skills", "firstSteps": "First Steps", "firstStepsDescription": "Answer your first question", "completed": "Completed!", "explorer": "Explorer", "explorerDescription": "Try 2 different universities", "student": "Student", "unknownSubject": "Unknown Subject", "university": "University", "answeredCorrectly": "You answered correctly", "answeredIncorrectly": "You answered incorrectly", "unlockContent": "Unlock all content", "upgradeDescription": "Upgrade to a premium plan to access all subjects, questions and exclusive content.", "viewPlans": "View Plans", "progressByUniversity": "Progress by University", "progressByUniversityDescription": "Your detailed performance separated by institution and subject", "overallProgress": "Overall Progress", "subjectDetails": "Subject Details", "subjectsPlural": "subjects", "subjectSingular": "subject", "questionsLabel": "Questions", "accuracyLabel": "Accuracy", "recentSubjects": "Recent Subjects", "continueWhere": "Continue where you left off", "questionsCount": "{count} questions", "correctPercentage": "{percentage}% correct", "lastActivity": "Last activity: {date}", "noSubjectsPracticed": "You haven't practiced any subjects yet", "startAnswering": "Start by answering some questions!", "viewAllSubjects": "View all subjects", "recentActivityTitle": "Recent Activity", "viewAll": "View All", "allTab": "All", "correctTab": "Correct", "incorrectTab": "Incorrect", "noRecentActivity": "No recent activity", "answerQuestions": "Answer some questions to see your activity!", "achievementsTitle": "Achievements", "achievementsSubtitle": "Keep earning achievements and improve your skills", "firstStep": "First Step", "firstStepDescription": "Complete your first question", "discipline": "Discipline", "disciplineDescription": "Study 3 days in a row", "explorerTitle": "Explorer", "explorerDesc": "Try 2 different universities", "genius": "<PERSON><PERSON>", "geniusDescription": "10 correct answers in a row", "advanced": "Advanced", "advancedDescription": "Reach level 8 in a subject", "user": "User", "question": "Question", "signOut": "Sign Out", "studentRole": "Student"}, "Questions": {"backToSubjects": "Back to subjects", "points": "points", "questionNumber": "Question {number}", "difficulty": "Difficulty: {level}", "questionImage": "Question image", "answer": "Answer", "finish": "Finish", "noQuestions": "No questions", "noQuestionsDescription": "No questions available for this subject.", "errorTitle": "Error", "errorDescription": "Could not load the question.", "loadingQuestion": "Loading question...", "correct": "Correct!", "incorrect": "Incorrect", "solutionExplained": "Solution explained", "solutionImage": "Solution image", "errorSubmitting": "Could not register your answer."}, "Subjects": {"title": "Explore Subjects", "description": "Choose a subject to start practicing questions", "searchPlaceholder": "Search subjects...", "searchResults": "Search Results", "noResults": "No subjects found matching \"{searchTerm}\"", "loading": "Loading..."}, "Progress": {"title": "Your progress {period}", "description": "Visualize and analyze your academic performance", "questionsAnswered": "Questions Answered", "accuracyPercentage": "Accuracy Percentage", "studyHours": "Study Hours", "averageLevel": "Average Level", "loading": "Loading...", "correct": "correct", "incorrect": "incorrect", "basedOn": "Based on {count} answers {period}", "noData": "No data {period}", "lastWeek": "in the last week", "lastMonth": "in the last month", "specificMonth": "in {month}", "allTime": "in total", "lastWeekOption": "Last Week", "lastMonthOption": "Last Month", "specificMonthOption": "By Month", "allTimeOption": "All Time", "subjectPerformance": "Performance by Subject", "difficultyDistribution": "Difficulty Distribution", "exportData": "Export Data", "subject": "Subject", "noProgressData": "No progress data yet", "answerQuestionsProgress": "Answer some questions to see your progress", "estimatedTime": "Estimated time", "average": "Average", "correctAnswersChart": "Correct answers", "incorrectAnswersChart": "Incorrect answers", "subjectLabel": "Subject", "quantityLabel": "Quantity", "questionsLabel": "questions", "studyTimeBySubject": "Study time by subject", "studyTimeDescription": "Hours dedicated to studying each subject", "estimatedHours": "Estimated hours", "noProgressDataPeriod": "No progress data", "answerQuestionsToSeeProgress": "Answer some questions to see your progress", "noDifficultyDataPeriod": "No difficulty data", "answerQuestionsToSeeDistribution": "Answer questions to see the distribution", "noTimeDataPeriod": "No time data", "timeEstimatedBasedOnQuestions": "Time is estimated based on answered questions", "level": "Level"}, "Profile": {"title": "User Profile", "description": "Manage your personal information and preferences", "profileTab": "Profile", "passwordTab": "Password", "personalInformation": "Personal Information", "personalInformationDescription": "Update your basic profile information", "personalInfo": "Personal Information", "personalInfoDescription": "Update your basic profile information", "profilePhoto": "Profile Photo", "profilePhotoDescription": "The functionality to change your photo will be added soon.", "profilePicture": "Profile Picture", "profilePictureDescription": "This will be displayed on your profile", "fullName": "Full Name", "fullNamePlaceholder": "Your name", "name": "Name", "nameDescription": "This is your public display name", "email": "Email", "emailPlaceholder": "<EMAIL>", "emailDescription": "This is the email associated with your account", "saveChanges": "Save Changes", "saving": "Saving...", "updateProfile": "Update Profile", "updating": "Updating...", "changePassword": "Change Password", "changePasswordDescription": "Update your password to keep your account secure", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "passwordRequirement": "Minimum 6 characters", "changePasswordButton": "Change Password", "sessionNotFound": "User session not found", "profileUpdated": "Profile Updated", "profileUpdatedDescription": "Your information has been updated successfully.", "errorUpdatingProfile": "Error updating profile", "errorUpdatingProfileDescription": "An error occurred while updating your profile.", "errorUpdating": "Error updating profile", "errorUpdatingDescription": "An error occurred while updating your profile.", "passwordUpdated": "Password Updated", "passwordUpdatedDescription": "Your password has been updated successfully.", "errorUpdatingPassword": "Error updating password", "errorUpdatingPasswordDescription": "An error occurred while updating your password.", "functionalityDisabled": "Functionality temporarily disabled", "nameMinLength": "Name must be at least 2 characters.", "emailInvalid": "Please enter a valid email address."}, "Subscription": {"title": "Your Subscription", "description": "Manage your plan and access all Manyas content", "activeSubscription": "Active Subscription", "activeSubscriptionDescription": "You currently have access to all premium content", "subscriptionIsActive": "Your subscription is active", "subscriptionActive": "Your subscription is active", "fullAccessMessage": "You have full access to all subjects and features.", "fullAccess": "You have full access to all subjects and features.", "manageSubscription": "Manage Subscription", "choosePlan": "Choose Your Plan", "choosePlanDescription": "Unlock full access to all subjects and premium features with one of our subscription plans.", "recommended": "Recommended", "subscribe": "Subscribe", "processing": "Processing...", "subscriptionActivated": "Subscription Activated!", "subscriptionActivatedDescription": "Your subscription has been activated successfully.", "errorActivatingSubscription": "Error activating subscription", "errorActivatingSubscriptionDescription": "An error occurred while processing the payment. Please try again.", "termsAndConditions": "By subscribing, you agree to our terms and conditions and privacy policy. You can cancel your subscription at any time."}, "Hero": {"badge": "University Preparation", "title": "Prepare for your", "titleHighlight": "dream university", "description": "With <PERSON><PERSON>, the platform designed specifically for Peruvian students seeking to enter the best universities in the country. Study efficiently, perform at your best.", "feature1": "Questions adapted to your level", "feature2": "Step-by-step explained solutions", "feature3": "Content specific by university", "startNow": "Start Now", "viewPlans": "View Plans", "cardTitle": "Your path to success", "cardDescription": "Join thousands of students who have achieved their goals with our platform."}, "Features": {"title": "Everything you need to", "titleHighlight": "pass your exam", "description": "We design each feature thinking about maximizing your learning and guaranteeing your success in the admission exam.", "feature1Title": "Updated Content", "feature1Description": "Educational material constantly updated according to the latest admission exams.", "feature2Title": "Progress Tracking", "feature2Description": "Visualize your progress with detailed statistics and personalized improvement areas.", "feature3Title": "University Specific", "feature3Description": "Questions and curriculum adapted to the requirements of each Peruvian university.", "feature4Title": "Adaptive Assessment", "feature4Description": "Questions that adapt to your knowledge level for efficient learning.", "feature5Title": "Detailed Explanations", "feature5Description": "Step-by-step solutions that help you understand concepts, not just memorize.", "feature6Title": "Multi-platform Access", "feature6Description": "Study from any device, anywhere and anytime."}, "Institutions": {"title": "Prepare for the", "titleHighlight": "best universities", "description": "Specialized content according to the profile of each Peruvian university, with questions based on previous exams.", "recentExams": "Recent exams", "explore": "Explore"}, "Testimonials": {"title": "What our", "titleHighlight": "students say", "description": "Discover how <PERSON><PERSON> has helped thousands of students achieve their goal of entering university.", "testimonial1Name": "<PERSON>", "testimonial1Position": "Medical Student - UNMSM", "testimonial1Quote": "Thanks to <PERSON><PERSON> I was able to prepare efficiently. The adaptive system helped me strengthen my weaknesses and make the most of my study time.", "testimonial2Name": "<PERSON>", "testimonial2Position": "Engineering Student - UNI", "testimonial2Quote": "What I liked most was the detailed solution guide. Understanding why I was wrong helped me improve quickly. I got in on my first try!", "testimonial3Name": "<PERSON>", "testimonial3Position": "Economics Student - PUCP", "testimonial3Quote": "The platform is very intuitive and the content is excellent. I recommend it to anyone who wants to prepare seriously for university."}, "Pricing": {"title": "Simple and", "titleHighlight": "transparent plans", "description": "Choose the plan that best fits your preparation needs and access all our content without restrictions.", "recommended": "Recommended", "startNow": "Start Now", "disclaimer": "All plans include 7 days trial. Cancel anytime, no commitment."}, "Plans": {"monthly": {"name": "Monthly", "description": "Complete access to all subjects for one month", "duration": "1 month", "features": {"0": "All subjects", "1": "Unlimited questions", "2": "Explained solutions", "3": "Progress tracking", "4": "Email support"}}, "quarterly": {"name": "Quarterly", "description": "Complete access to all subjects for three months", "duration": "3 months", "savings": "Save 25%!", "features": {"0": "All subjects", "1": "Unlimited questions", "2": "Explained solutions", "3": "Progress tracking", "4": "Priority support", "5": "Exclusive virtual seminars"}}}, "Footer": {"description": "The university preparation platform created by and for Peruvian students.", "platformTitle": "Platform", "features": "Features", "pricing": "Pricing", "register": "Sign Up", "login": "Sign In", "resourcesTitle": "Resources", "blog": "Blog", "studyGuides": "Study Guides", "examCalendar": "Exam Calendar", "testimonials": "Testimonials", "helpTitle": "Help", "contact": "Contact Us", "faq": "Frequently Asked Questions", "terms": "Terms and Conditions", "privacy": "Privacy Policy", "copyright": "All rights reserved."}}