"use client";

import { useState, useEffect } from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { useTheme } from 'next-themes';
import { supabase } from '@/lib/supabase/client';
import { getUserProgressData } from '@/lib/queries/questions';
import { useTranslations } from 'next-intl';

interface PerformanceChartProps {
  userId?: string;
}

interface ChartDataPoint {
  date: string;
  [subject: string]: string | number;
}

interface ColorConfig {
  [key: string]: string;
}

export function PerformanceChart({ userId }: PerformanceChartProps) {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [data, setData] = useState<ChartDataPoint[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const t = useTranslations('Progress');
  
  // Theme-dependent colors
  const [colors, setColors] = useState<ColorConfig>({
    matemática: '#2563eb', // blue-600
    física: '#9333ea',     // purple-600
    química: '#16a34a',    // green-600
    historia: '#d97706',   // amber-600
    grid: '#e5e7eb',       // gray-200
    text: '#6b7280',       // gray-500
  });

  useEffect(() => {
    setMounted(true);

    // Update colors based on theme
    if (theme === 'dark') {
      setColors({
        matemática: '#3b82f6', // blue-500
        física: '#a855f7',     // purple-500
        química: '#22c55e',    // green-500
        historia: '#f59e0b',   // amber-500
        grid: '#374151',       // gray-700
        text: '#9ca3af',       // gray-400
      });
    } else {
      setColors({
        matemática: '#2563eb', // blue-600
        física: '#9333ea',     // purple-600
        química: '#16a34a',    // green-600
        historia: '#d97706',   // amber-600
        grid: '#e5e7eb',       // gray-200
        text: '#6b7280',       // gray-500
      });
    }
  }, [theme]);

  // Fetch real data
  useEffect(() => {
    async function fetchData() {
      if (!userId) {
        // Get current user session
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.user) {
          setIsLoading(false);
          return;
        }

        try {
          const progressData = await getUserProgressData(session.user.id);
          setData(progressData);
        } catch (error) {
          console.error('Error fetching progress data:', error);
          setData([]);
        }
      } else {
        try {
          const progressData = await getUserProgressData(userId);
          setData(progressData);
        } catch (error) {
          console.error('Error fetching progress data:', error);
          setData([]);
        }
      }
      setIsLoading(false);
    }

    if (mounted) {
      fetchData();
    }
  }, [mounted, userId]);

  if (!mounted || isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-center">
        <div>
          <p className="text-gray-500 dark:text-gray-400 mb-2">{t('noProgressData')}</p>
          <p className="text-sm text-gray-400 dark:text-gray-500">{t('answerQuestionsProgress')}</p>
        </div>
      </div>
    );
  }

  // Get unique subjects from data
  const subjects = data.length > 0 ? Object.keys(data[0]).filter(key => key !== 'date') : [];

  // Color mapping for subjects
  const subjectColors: ColorConfig = {
    'matemática': colors.matemática,
    'física': colors.física,
    'química': colors.química,
    'historia': colors.historia,
    'geometría': '#06b6d4', // cyan-500
    'literatura': '#ec4899', // pink-500
  };

  const customTooltipFormatter = (value: number | string, name: string): [string, string] => [
    `${value}%`,
    name.charAt(0).toUpperCase() + name.slice(1)
  ];

  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart data={data} margin={{ top: 10, right: 20, left: -10, bottom: 0 }}>
        <defs>
          {subjects.map(subject => (
            <linearGradient key={subject} id={subject} x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={subjectColors[subject] || '#6b7280'} stopOpacity={0.3} />
              <stop offset="95%" stopColor={subjectColors[subject] || '#6b7280'} stopOpacity={0} />
            </linearGradient>
          ))}
        </defs>
        <XAxis
          dataKey="date"
          tickLine={false}
          axisLine={false}
          tick={{ fill: colors.text, fontSize: 12 }}
        />
        <YAxis
          tickLine={false}
          axisLine={false}
          tick={{ fill: colors.text, fontSize: 12 }}
          domain={[0, 100]}
          ticks={[0, 25, 50, 75, 100]}
        />
        <CartesianGrid
          strokeDasharray="3 3"
          stroke={colors.grid}
          vertical={false}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
            borderColor: theme === 'dark' ? '#374151' : '#e5e7eb',
            borderRadius: '0.375rem',
            fontSize: '0.875rem',
            color: theme === 'dark' ? '#e5e7eb' : '#374151'
          }}
          formatter={customTooltipFormatter}
        />
        {subjects.map(subject => (
          <Area
            key={subject}
            type="monotone"
            dataKey={subject}
            stroke={subjectColors[subject] || '#6b7280'}
            fill={`url(#${subject})`}
            strokeWidth={2}
          />
        ))}
      </AreaChart>
    </ResponsiveContainer>
  );
}