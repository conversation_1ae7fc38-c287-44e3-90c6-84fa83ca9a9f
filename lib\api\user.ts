// Client-side API functions for user operations
export async function updateUserProfileAPI(userId: string, data: any) {
  const response = await fetch('/api/user/profile', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ userId, data }),
  });

  const result = await response.json();
  
  if (!response.ok) {
    throw new Error(result.error || 'Update profile failed');
  }
  
  return result;
}

export async function getUserSubscriptionAPI(userId: string) {
  const response = await fetch(`/api/user/subscription?userId=${userId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(data.error || 'Get subscription failed');
  }
  
  return data;
}

export async function updateUserSubscriptionAPI(userId: string, subscriptionData: any) {
  const response = await fetch('/api/user/subscription', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ userId, subscriptionData }),
  });

  const result = await response.json();
  
  if (!response.ok) {
    throw new Error(result.error || 'Update subscription failed');
  }
  
  return result;
}

export async function updateUserProgressAPI(userId: string, subjectId: number, wasCorrect: boolean, timeSpentSeconds: number) {
  const response = await fetch('/api/user/progress', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ userId, subjectId, wasCorrect, timeSpentSeconds }),
  });

  const result = await response.json();
  
  if (!response.ok) {
    throw new Error(result.error || 'Update progress failed');
  }
  
  return result;
}