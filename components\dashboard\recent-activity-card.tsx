import { CheckCircle, XCircle } from 'lucide-react';

interface RecentActivityCardProps {
  title: string;
  description: string;
  timestamp: string;
  isCorrect: boolean;
}

export function RecentActivityCard({
  title,
  description,
  timestamp,
  isCorrect
}: RecentActivityCardProps) {
  return (
    <div className="flex items-start gap-3 p-3 rounded-lg border border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-900/50 transition-colors">
      <div className={`mt-0.5 flex-shrink-0 rounded-full p-1.5 ${isCorrect ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-500' : 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-500'}`}>
        {isCorrect ? (
          <CheckCircle className="h-4 w-4" />
        ) : (
          <XCircle className="h-4 w-4" />
        )}
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate">{title}</p>
        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">{description}</p>
        <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">{timestamp}</p>
      </div>
    </div>
  );
}