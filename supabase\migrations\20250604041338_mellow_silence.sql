/*
  # Create subjects table

  1. New Tables
    - `subjects`
      - `id` (integer, primary key)
      - `institution_id` (integer, references institution.id)
      - `name` (text, not null)
  2. Security
    - Enable RLS on `subjects` table
    - Add policy for public read access
*/

CREATE TABLE IF NOT EXISTS subjects (
  id serial PRIMARY KEY,
  institution_id integer REFERENCES institutions(id) NOT NULL,
  name text NOT NULL
);

ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read subjects"
  ON subjects
  FOR SELECT
  TO anon, authenticated
  USING (true);