"use client";

import { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface MathRendererProps {
  content: string;
  className?: string;
}

declare global {
  interface Window {
    MathJax: {
      typesetPromise: (elements?: Element[]) => Promise<void>;
      startup: {
        defaultReady: () => void;
      };
    };
  }
}

export function MathRenderer({ content, className }: MathRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const renderMath = async () => {
      if (typeof window !== 'undefined' && window.MathJax && containerRef.current) {
        try {
          await window.MathJax.typesetPromise([containerRef.current]);
        } catch (error) {
          console.error('MathJax rendering error:', error);
        }
      }
    };

    // Small delay to ensure content is rendered
    const timer = setTimeout(renderMath, 100);
    return () => clearTimeout(timer);
  }, [content]);

  return (
    <div 
      ref={containerRef}
      className={cn("math-content", className)}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}