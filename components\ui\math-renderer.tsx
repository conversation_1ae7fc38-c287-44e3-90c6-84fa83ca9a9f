"use client";

import { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface MathRendererProps {
  content: string;
  className?: string;
}

declare global {
  interface Window {
    MathJax: any;
  }
}

export function MathRenderer({ content, className }: MathRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current && window.MathJax) {
      // Set the content first
      containerRef.current.innerHTML = content;
      
      // Then process the math
      if (window.MathJax.typesetPromise) {
        window.MathJax.typesetPromise([containerRef.current]).catch((err: any) => {
          console.error('MathJax typeset error:', err);
        });
      } else if (window.MathJax.Hub) {
        // Fallback for older MathJax versions
        window.MathJax.Hub.Queue(['Typeset', window.MathJax.Hub, containerRef.current]);
      }
    }
  }, [content]);

  // Fallback rendering if MathJax is not available
  if (typeof window !== 'undefined' && !window.MathJax) {
    return (
      <div 
        ref={containerRef}
        className={cn("math-content", className)}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  }

  return (
    <div 
      ref={containerRef}
      className={cn("math-content", className)}
    />
  );
}