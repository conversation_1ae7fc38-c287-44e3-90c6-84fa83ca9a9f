/*
  # Create questions table

  1. New Tables
    - `questions`
      - `id` (integer, primary key)
      - `question` (text, not null) - stores HTML content
      - `options` (jsonb, not null) - stores answer options
      - `solution` (text, not null) - stores HTML solution
      - `image` (text, nullable) - stores image URL
      - `subject_id` (integer, references subjects.id)
      - `difficulty` (integer, not null) - from 1 to 10
      - `created_at` (timestamptz, default: now())
  2. Security
    - Enable RLS on `questions` table
    - Add policy for public read access
*/

CREATE TABLE IF NOT EXISTS questions (
  id serial PRIMARY KEY,
  question text NOT NULL,
  question_image_url text,
  options jsonb NOT NULL,
  correct_answer integer NOT NULL,
  solution text,
  solution_image_url text,
  image text,
  subject_id integer REFERENCES subjects(id) NOT NULL,
  difficulty numeric NOT NULL CHECK (difficulty BETWEEN 1 AND 10),
  created_at timestamptz DEFAULT now(),
  source_id text REFERENCES sources(id),
  topic_id text REFERENCES topics(id),
);

ALTER TABLE questions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read questions"
  ON questions
  FOR SELECT
  TO anon, authenticated
  USING (true);