"use client";

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Link, usePathname } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import {
  GraduationCap,
  LayoutDashboard,
  BookOpen,
  BarChart,
  CreditCard,
  User,
  LogOut,
  MenuIcon,
  X
} from 'lucide-react';
import { APP_NAME } from '@/lib/constants';
import { supabase, getSession } from '@/lib/supabase/client';

export function DashboardSidebar() {
  const pathname = usePathname();
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [userName, setUserName] = useState<string>("");
  const t = useTranslations('Dashboard');

  const sidebarItems = [
    {
      title: t('title'),
      href: '/dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      title: t('subjects'),
      href: '/subjects',
      icon: <BookOpen className="h-5 w-5" />,
    },
    {
      title: t('progress'),
      href: '/progress',
      icon: <BarChart className="h-5 w-5" />,
    },
    {
      title: t('subscription'),
      href: '/subscription',
      icon: <CreditCard className="h-5 w-5" />,
    },
    {
      title: t('profile'),
      href: '/profile',
      icon: <User className="h-5 w-5" />,
    },
  ];
  
  useEffect(() => {
    async function fetchUserProfile() {
      try {
        console.log('Sidebar: Getting session...');
        const session = await getSession();
        console.log('Sidebar: Session result:', session);

        if (session?.user) {
          console.log('Sidebar: User found:', session.user);
          const name = session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || t('user');
          console.log('Sidebar: Setting name:', name);
          setUserName(name);
        } else {
          console.log('Sidebar: No session found');
          setUserName(t('user'));
        }
      } catch (error) {
        console.error('Sidebar: Error fetching user profile:', error);
        setUserName(t('user'));
      }
    }

    fetchUserProfile();
  }, [t]);
  
  async function handleLogout() {
    try {
      console.log('Sidebar: Signing out...');
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Sidebar: Error signing out:', error);
      }
      // Force redirect to login page
      window.location.href = '/login';
    } catch (error) {
      console.error('Sidebar: Error signing out:', error);
      window.location.href = '/login';
    }
  }

  return (
    <>
      {/* Mobile toggle button */}
      <Button
        variant="outline"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setIsMobileOpen(!isMobileOpen)}
      >
        {isMobileOpen ? (
          <X className="h-5 w-5" />
        ) : (
          <MenuIcon className="h-5 w-5" />
        )}
      </Button>

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-40 w-64 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 transition-transform duration-300 md:translate-x-0",
          isMobileOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex flex-col h-full">
          <div className="p-4 border-b border-gray-200 dark:border-gray-800">
            <Link href="/dashboard" className="flex items-center gap-2 font-bold text-xl">
              <GraduationCap className="h-6 w-6" />
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                {APP_NAME}
              </span>
            </Link>
          </div>

          <div className="flex-1 overflow-y-auto py-4 px-3">
            <nav className="space-y-1">
              {sidebarItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                    pathname === item.href
                      ? "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-400"
                      : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"
                  )}
                >
                  {item.icon}
                  {item.title}
                </Link>
              ))}
            </nav>
          </div>

          <div className="p-4 border-t border-gray-200 dark:border-gray-800">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-9 h-9 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-800 dark:text-blue-300 font-medium">
                {userName ? userName.charAt(0).toUpperCase() : "U"}
              </div>
              <div className="overflow-hidden">
                <p className="text-sm font-medium truncate">{userName || t('user')}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {t('studentRole')}
                </p>
              </div>
            </div>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full flex items-center gap-2 justify-center"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4" />
              {t('signOut')}
            </Button>
          </div>
        </div>
      </aside>

      {/* Overlay for mobile */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black/30 z-30 md:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Spacer for content (only visible on desktop) */}
      <div className="hidden md:block w-64 shrink-0" />
    </>
  );
}