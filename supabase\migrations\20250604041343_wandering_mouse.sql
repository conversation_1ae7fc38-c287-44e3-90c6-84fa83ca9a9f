/*
  # Create user_progress table

  1. New Tables
    - `user_progress`
      - `id` (integer, primary key)
      - `user_id` (uuid, references users.id)
      - `subject_id` (integer, references subjects.id)
      - `correct_answers` (integer, default: 0)
      - `incorrect_answers` (integer, default: 0)
      - `last_updated` (timestamptz, default: now())
  2. Security
    - Enable RLS on `user_progress` table
    - Add policies for authenticated users to manage their own progress data
*/

CREATE TABLE IF NOT EXISTS user_progress (
  id serial PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  subject_id integer REFERENCES subjects(id) NOT NULL,
  correct_answers integer DEFAULT 0 NOT NULL,
  incorrect_answers integer DEFAULT 0 NOT NULL,
  last_updated timestamptz DEFAULT now() NOT NULL,
  user_level numeric NOT NULL DEFAULT 3 CHECK (user_level BETWEEN 0 AND 10),
  hours numeric NOT NULL DEFAULT 0 CHECK (hours >= 0),
  UNIQUE (user_id, subject_id)
);

ALTER TABLE user_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own progress"
  ON user_progress
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update own progress"
  ON user_progress
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own progress"
  ON user_progress
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);