import { getRequestConfig } from 'next-intl/server';
import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
 // This will either be the locale from the URL or the default locale
 let locale: string;
 
 try {
   // Await the requestLocale promise
   const requested = await requestLocale;
   
   // Check if the requested locale is supported
   locale = routing.locales.includes(requested as any) ? requested as string : routing.defaultLocale;
 } catch (error) {
   // Fallback to default locale if there's an error
   locale = routing.defaultLocale;
 }

 return {
  locale,
  messages: (await import(`../messages/${locale}.json`)).default
 };
});