/*
  # Add institution_id to sources table

  1. Changes
    - Add `institution_id` column to `sources` table
    - Make it a nullable foreign key to `institutions.id`
    
  2. Security
    - No changes to existing RLS policies
*/

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'sources' AND column_name = 'institution_id'
  ) THEN
    ALTER TABLE sources ADD COLUMN institution_id text REFERENCES institutions(id);
  END IF;
END $$;