"use client";

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle2, Loader2 } from 'lucide-react';
import { useSubscriptionPlans } from '@/lib/plans';
import { supabase } from '@/lib/supabase/client';
import { useToast } from "@/hooks/use-toast";

export default function SubscriptionPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const { toast } = useToast();
  const t = useTranslations('Subscription');
  const plans = useSubscriptionPlans();
  
  useEffect(() => {
    async function getUserData() {
      try {
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user) {
          setUserId(session.user.id);
          setSubscriptionStatus(session.user.user_metadata?.subscription_status || 'inactive');
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    }

    getUserData();
  }, []);

  const handleSubscribe = async (planId: string) => {
    if (!userId) return;
    
    setIsLoading(true);
    setSelectedPlan(planId);
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Calculate expiration date
      let expirationDate = new Date();
      if (planId === 'monthly') {
        expirationDate.setMonth(expirationDate.getMonth() + 1);
      } else if (planId === 'quarterly') {
        expirationDate.setMonth(expirationDate.getMonth() + 3);
      }
      
      // Update user subscription status
      const { error } = await supabase.auth.updateUser({
        data: {
          subscription_status: 'active',
          subscription_expires: expirationDate.toISOString()
        }
      });

      if (error) {
        throw error;
      }
      
      setSubscriptionStatus('active');
      toast({
        title: t('subscriptionActivated'),
        description: t('subscriptionActivatedDescription'),
      });
    } catch (error) {
      console.error('Error updating subscription:', error);
      toast({
        variant: "destructive",
        title: t('errorActivatingSubscription'),
        description: t('errorActivatingSubscriptionDescription'),
      });
    } finally {
      setIsLoading(false);
      setSelectedPlan(null);
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold tracking-tight mb-1">
          {t('title')}
        </h2>
        <p className="text-muted-foreground">
          {t('description')}
        </p>
      </div>
      
      {subscriptionStatus === 'active' ? (
        <Card>
          <CardHeader>
            <CardTitle>{t('activeSubscription')}</CardTitle>
            <CardDescription>
              {t('activeSubscriptionDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-green-500" />
              <span className="font-medium">{t('subscriptionIsActive')}</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
              {t('fullAccessMessage')}
            </p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">
              {t('manageSubscription')}
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <>
          <div className="text-center mb-8">
            <h3 className="text-xl font-medium mb-2">{t('choosePlan')}</h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-lg mx-auto">
              {t('choosePlanDescription')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {plans.map((plan) => (
              <div key={plan.id} className="relative">
                <Card
                  className={`h-full ${plan.popular ? 'border-2 border-blue-500 dark:border-blue-600 shadow-lg' : ''}`}
                >
                  {plan.popular && (
                    <div className="absolute -top-2 -right-2 z-10">
                      <span className="inline-flex items-center justify-center px-3 py-1 text-xs font-medium text-white bg-blue-600 rounded-full">
                        {t('recommended')}
                      </span>
                    </div>
                  )}
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="mb-6">
                    <p className="text-3xl font-bold">{plan.price}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{plan.duration}</p>
                    {plan.savings && (
                      <p className="text-sm font-medium text-green-600 mt-1">{plan.savings}</p>
                    )}
                  </div>
                  <ul className="space-y-3">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full"
                    onClick={() => handleSubscribe(plan.id)}
                    disabled={isLoading}
                  >
                    {isLoading && selectedPlan === plan.id ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t('processing')}
                      </>
                    ) : (
                      t('subscribe')
                    )}
                  </Button>
                </CardFooter>
              </Card>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-8">
            <p className="text-sm text-gray-500 dark:text-gray-400 max-w-lg mx-auto">
              {t('termsAndConditions')}
            </p>
          </div>
        </>
      )}
    </div>
  );
}