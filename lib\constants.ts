export const APP_NAME = "Manyas";

export const SUBSCRIPTION_PLANS = [
  {
    id: "monthly",
    name: "<PERSON><PERSON><PERSON>",
    description: "Acceso completo a todas las materias por un mes",
    price: "S/. 89.90",
    features: [
      "Todas las materias",
      "Preguntas ilimitadas",
      "Soluciones explicadas",
      "Seguimiento de progreso",
      "Soporte por email"
    ],
    duration: "1 mes",
    popular: false
  },
  {
    id: "quarterly",
    name: "Trimestral",
    description: "Acceso completo a todas las materias por tres meses",
    price: "S/. 199.90",
    features: [
      "Todas las materias",
      "Preguntas ilimitadas",
      "Soluciones explicadas",
      "Seguimiento de progreso",
      "Soporte prioritario",
      "Seminarios virtuales exclusivos"
    ],
    duration: "3 meses",
    popular: true,
    savings: "¡Ahorra 25%!"
  }
];

export const INSTITUTIONS = [
  { 
    id: 1, 
    name: "Universidad Nacional Mayor de San Marcos",
    shortName: "UNMSM",
    image: "https://images.pexels.com/photos/207692/pexels-photo-207692.jpeg"
  },
  { 
    id: 2, 
    name: "Universidad Nacional de Ingeniería",
    shortName: "UNI",
    image: "https://images.pexels.com/photos/356079/pexels-photo-356079.jpeg"
  },
  { 
    id: 3, 
    name: "Pontificia Universidad Católica del Perú",
    shortName: "PUCP",
    image: "https://images.pexels.com/photos/256490/pexels-photo-256490.jpeg"
  },
  { 
    id: 4, 
    name: "Universidad Nacional Agraria La Molina",
    shortName: "UNALM",
    image: "https://images.pexels.com/photos/267885/pexels-photo-267885.jpeg"
  }
];

export const SUBJECTS = [
  { 
    id: 1, 
    name: "Matemática", 
    institution_id: 1,
    icon: "Calculator",
    description: "Álgebra, aritmética, trigonometría y cálculo",
    color: "bg-blue-100 dark:bg-blue-950" 
  },
  { 
    id: 2, 
    name: "Física", 
    institution_id: 1,
    icon: "Atom",
    description: "Mecánica, electricidad, magnetismo y más",
    color: "bg-purple-100 dark:bg-purple-950" 
  },
  { 
    id: 3, 
    name: "Química", 
    institution_id: 1,
    icon: "Flask",
    description: "Química orgánica, inorgánica y analítica",
    color: "bg-green-100 dark:bg-green-950" 
  },
  { 
    id: 4, 
    name: "Historia", 
    institution_id: 1,
    icon: "Landmark",
    description: "Historia del Perú y universal",
    color: "bg-amber-100 dark:bg-amber-950" 
  },
  { 
    id: 5, 
    name: "Geometría", 
    institution_id: 2,
    icon: "Triangle",
    description: "Geometría plana, del espacio y analítica",
    color: "bg-red-100 dark:bg-red-950" 
  },
  { 
    id: 6, 
    name: "Literatura", 
    institution_id: 3,
    icon: "BookOpen",
    description: "Literatura peruana y universal",
    color: "bg-pink-100 dark:bg-pink-950" 
  }
];