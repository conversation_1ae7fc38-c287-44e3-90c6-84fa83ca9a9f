import { config } from '@/lib/config/environment';

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

class Logger {
  private shouldLog(level: LogLevel): boolean {
    if (!config.logging.enableConsole) return false;
    
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };
    
    const currentLevel = levels[config.logging.level as LogLevel] || 0;
    return levels[level] >= currentLevel;
  }

  debug(message: string, ...args: any[]) {
    if (this.shouldLog('debug')) {
      console.log(`[DEBUG] ${message}`, ...args);
    }
  }

  info(message: string, ...args: any[]) {
    if (this.shouldLog('info')) {
      console.info(`[INFO] ${message}`, ...args);
    }
  }

  warn(message: string, ...args: any[]) {
    if (this.shouldLog('warn')) {
      console.warn(`[WARN] ${message}`, ...args);
    }
  }

  error(message: string, ...args: any[]) {
    if (this.shouldLog('error')) {
      console.error(`[ERROR] ${message}`, ...args);
    }
    
    // En producción, aquí podrías enviar errores a un servicio como Sentry
    if (config.isProduction && config.features.enableErrorReporting) {
      // Implementar reporte de errores
      this.reportError(message, args);
    }
  }

  private reportError(message: string, args: any[]) {
    // Implementar integración con servicio de reporte de errores
    // Por ejemplo: Sentry, LogRocket, etc.
  }
}

export const logger = new Logger();