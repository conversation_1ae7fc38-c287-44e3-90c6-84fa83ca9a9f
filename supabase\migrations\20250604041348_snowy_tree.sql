/*
  # Create responses table

  1. New Tables
    - `responses`
      - `id` (integer, primary key)
      - `user_id` (uuid, references users.id)
      - `question_id` (integer, references questions.id)
      - `response` (text, not null) - user's selected option
      - `was_correct` (boolean, not null)
      - `created_at` (timestamptz, default: now())
  2. Security
    - Enable RLS on `responses` table
    - Add policies for authenticated users to manage their own responses
*/

CREATE TABLE IF NOT EXISTS responses (
  id serial PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  question_id integer REFERENCES questions(id) NOT NULL,
  response integer NOT NULL,
  was_correct boolean NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL
);

ALTER TABLE responses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own responses"
  ON responses
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own responses"
  ON responses
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);