"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Session } from '@supabase/auth-helpers-nextjs';

interface SessionContextType {
  session: Session | null;
  loading: boolean;
  error: string | null;
  refreshSession: () => Promise<void>;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

// Global session cache to prevent multiple simultaneous requests
let globalSessionCache: Session | null = null;
let globalSessionPromise: Promise<Session | null> | null = null;
let lastSessionFetch = 0;
const SESSION_CACHE_DURATION = 30000; // 30 seconds
const MIN_FETCH_INTERVAL = 2000; // 2 seconds minimum between fetches

interface SessionProviderProps {
  children: ReactNode;
}

export function SessionProvider({ children }: SessionProviderProps) {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSessionSafely = async (): Promise<Session | null> => {
    const now = Date.now();
    
    // Return cached session if still valid
    if (globalSessionCache && (now - lastSessionFetch) < SESSION_CACHE_DURATION) {
      return globalSessionCache;
    }
    
    // If there's already a request in progress, wait for it
    if (globalSessionPromise) {
      return await globalSessionPromise;
    }
    
    // Enforce minimum interval between requests
    const timeSinceLastFetch = now - lastSessionFetch;
    if (timeSinceLastFetch < MIN_FETCH_INTERVAL) {
      const waitTime = MIN_FETCH_INTERVAL - timeSinceLastFetch;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    // Create new request
    globalSessionPromise = (async () => {
      try {
        lastSessionFetch = Date.now();
        const supabase = createClientComponentClient();
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          if (error.message?.includes('rate limit') || error.status === 429) {
            console.warn('Rate limit reached, using cached session');
            return globalSessionCache;
          }
          
          // Handle refresh token errors
          if (error.code === 'refresh_token_not_found' || error.message?.includes('refresh_token_not_found')) {
            console.warn('Invalid refresh token, clearing session');
            globalSessionCache = null;
            await clearInvalidSession();
            return null;
          }
          
          throw error;
        }
        
        globalSessionCache = session;
        return session;
      } catch (error: any) {
        if (error.message?.includes('rate limit') || error.status === 429) {
          console.warn('Rate limit reached, using cached session');
          return globalSessionCache;
        }
        
        // Handle refresh token errors
        if (error.code === 'refresh_token_not_found' || error.message?.includes('refresh_token_not_found')) {
          console.warn('Invalid refresh token, clearing session');
          globalSessionCache = null;
          await clearInvalidSession();
          return null;
        }
        
        throw error;
      } finally {
        globalSessionPromise = null;
      }
    })();
    
    return await globalSessionPromise;
  };

  const refreshSession = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Clear cache to force fresh fetch
      globalSessionCache = null;
      lastSessionFetch = 0;
      
      const newSession = await fetchSessionSafely();
      setSession(newSession);
    } catch (error: any) {
      console.error('Error refreshing session:', error);
      if (error.message?.includes('rate limit') || error.status === 429) {
        setError('Rate limit reached. Please wait a moment before refreshing.');
      } else if (error.code === 'refresh_token_not_found') {
        setError('Session expired. Please log in again.');
        await clearInvalidSession();
      } else {
        setError('Failed to refresh session');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let mounted = true;
    
    const initializeSession = async () => {
      try {
        const initialSession = await fetchSessionSafely();
        if (mounted) {
          setSession(initialSession);
          setError(null);
        }
      } catch (error: any) {
        console.error('Error initializing session:', error);
        if (mounted) {
          if (error.message?.includes('rate limit') || error.status === 429) {
            setError('Rate limit reached. Please wait a moment.');
          } else if (error.code === 'refresh_token_not_found') {
            setError('Session expired. Please log in again.');
            await clearInvalidSession();
          } else {
            setError('Failed to initialize session');
          }
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    initializeSession();

    // Set up auth state change listener with rate limiting
    const supabase = createClientComponentClient();
    let lastAuthChange = 0;
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        const now = Date.now();
        
        // Rate limit auth state changes
        if (now - lastAuthChange < 1000) {
          return;
        }
        lastAuthChange = now;
        
        if (mounted) {
          console.log('Auth state changed:', event);
          globalSessionCache = newSession;
          lastSessionFetch = now;
          setSession(newSession);
          setError(null);
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const value: SessionContextType = {
    session,
    loading,
    error,
    refreshSession,
  };

  return (
    <SessionContext.Provider value={value}>
      {children}
    </SessionContext.Provider>
  );
}

export function useSession() {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
}

export async function clearInvalidSession() {
  try {
    globalSessionCache = null;
    lastSessionFetch = 0;
    
    const supabase = createClientComponentClient();
    await supabase.auth.signOut();
    
    // Clear any stored tokens from localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('supabase.auth.token');
      // Clear Supabase auth tokens with dynamic key
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.includes('supabase') && key.includes('auth')) {
          localStorage.removeItem(key);
        }
      });
    }
  } catch (error) {
    console.warn('Error clearing invalid session:', error);
  }
}
