/*
  # Add sources and topics tables with question relationships

  1. New Tables
    - `sources`
      - `id` (uuid, primary key)
      - `name` (text, not null)
      - `date` (date, not null)
      - `created_at` (timestamptz, default: now())
    - `topics`
      - `id` (uuid, primary key)
      - `name` (text, not null)
      - `created_at` (timestamptz, default: now())

  2. Changes to existing tables
    - Add `source_id` (uuid, references sources.id) to questions table
    - Add `topic_id` (uuid, references topics.id) to questions table

  3. Security
    - Enable RLS on both new tables
    - Add policies for public read access
*/

-- Create sources table
CREATE TABLE IF NOT EXISTS sources (
  id text PRIMARY KEY,
  name text NOT NULL,
  date date NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL
);

ALTER TABLE sources ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read sources"
  ON sources
  FOR SELECT
  TO anon, authenticated
  USING (true);

-- Create topics table
CREATE TABLE IF NOT EXISTS topics (
  id text PRIMARY KEY,
  name text NOT NULL,
  created_at timestamptz DEFAULT now() NOT NULL
);

ALTER TABLE topics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read topics"
  ON topics
  FOR SELECT
  TO anon, authenticated
  USING (true);

-- Add foreign key columns to questions table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'questions' AND column_name = 'source_id'
  ) THEN
    ALTER TABLE questions ADD COLUMN source_id text REFERENCES sources(id);
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'questions' AND column_name = 'topic_id'
  ) THEN
    ALTER TABLE questions ADD COLUMN topic_id text REFERENCES topics(id);
  END IF;
END $$;