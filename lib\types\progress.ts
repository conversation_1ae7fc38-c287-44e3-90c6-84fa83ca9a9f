// Shared types for progress and statistics

export interface SubjectStats {
  subject_id: number;
  subject_name: string;
  correct_answers: number;
  incorrect_answers: number;
  user_level: number;
  hours: number;
  total_answers: number;
  accuracy: number;
}

export interface InstitutionStats {
  institution_id: string;
  institution_name: string;
  subjects: SubjectStats[];
  total_answers: number;
  total_correct: number;
  total_hours: number;
  total_level: number;
  subjects_count: number;
  average_accuracy: number;
  average_level: number;
}

export interface UserProgress {
  id: string;
  user_id: string;
  subject_id: number;
  correct_answers: number;
  incorrect_answers: number;
  user_level: number;
  hours: number;
  last_updated: string;
  subjects?: {
    id: number;
    name: string;
    institution_id: string;
  };
  subject?: {
    id: number;
    name: string;
    institution_id: string;
  };
  institution?: {
    id: string;
    name: string;
  };
}

export interface ProcessedResponse {
  created_at: string;
  was_correct: boolean;
  subject_name: string;
}
