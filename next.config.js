const createNextIntPlugin = require("next-intl/plugin");

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.pexels.com",
        port: "",
        pathname: "/**",
      },
    ],
  },
  // Configuración de headers de seguridad para producción
  async headers() {
    if (process.env.NODE_ENV === 'production') {
      return [
        {
          source: '/(.*)',
          headers: [
            {
              key: 'X-Frame-Options',
              value: 'DENY',
            },
            {
              key: 'X-Content-Type-Options',
              value: 'nosniff',
            },
            {
              key: 'Referrer-Policy',
              value: 'origin-when-cross-origin',
            },
          ],
        },
      ];
    }
    return [];
  },
  // Configuración de redirects
  async redirects() {
    return [
      // Redirects específicos por ambiente si es necesario
    ];
  },
};

const withNextIntl = createNextIntPlugin();
module.exports = withNextIntl(nextConfig);