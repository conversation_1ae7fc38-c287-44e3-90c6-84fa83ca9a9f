import { useTranslations } from 'next-intl';

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: string;
  features: string[];
  duration: string;
  popular: boolean;
  savings?: string;
}

export const PLAN_PRICES = {
  monthly: "S/. 89.90",
  quarterly: "S/. 199.90"
};

export function useSubscriptionPlans(): SubscriptionPlan[] {
  const t = useTranslations('Plans');

  // Get features arrays manually to handle next-intl array translation
  const monthlyFeatures = [
    t('monthly.features.0'),
    t('monthly.features.1'),
    t('monthly.features.2'),
    t('monthly.features.3'),
    t('monthly.features.4')
  ];

  const quarterlyFeatures = [
    t('quarterly.features.0'),
    t('quarterly.features.1'),
    t('quarterly.features.2'),
    t('quarterly.features.3'),
    t('quarterly.features.4'),
    t('quarterly.features.5')
  ];

  return [
    {
      id: "monthly",
      name: t('monthly.name'),
      description: t('monthly.description'),
      price: PLAN_PRICES.monthly,
      features: monthlyFeatures,
      duration: t('monthly.duration'),
      popular: false
    },
    {
      id: "quarterly",
      name: t('quarterly.name'),
      description: t('quarterly.description'),
      price: PLAN_PRICES.quarterly,
      features: quarterlyFeatures,
      duration: t('quarterly.duration'),
      popular: true,
      savings: t('quarterly.savings')
    }
  ];
}

// For server-side usage
export function getSubscriptionPlans(t: any): SubscriptionPlan[] {
  // Get features arrays manually to handle next-intl array translation
  const monthlyFeatures = [
    t('monthly.features.0'),
    t('monthly.features.1'),
    t('monthly.features.2'),
    t('monthly.features.3'),
    t('monthly.features.4')
  ];

  const quarterlyFeatures = [
    t('quarterly.features.0'),
    t('quarterly.features.1'),
    t('quarterly.features.2'),
    t('quarterly.features.3'),
    t('quarterly.features.4'),
    t('quarterly.features.5')
  ];

  return [
    {
      id: "monthly",
      name: t('monthly.name'),
      description: t('monthly.description'),
      price: PLAN_PRICES.monthly,
      features: monthlyFeatures,
      duration: t('monthly.duration'),
      popular: false
    },
    {
      id: "quarterly",
      name: t('quarterly.name'),
      description: t('quarterly.description'),
      price: PLAN_PRICES.quarterly,
      features: quarterlyFeatures,
      duration: t('quarterly.duration'),
      popular: true,
      savings: t('quarterly.savings')
    }
  ];
}
