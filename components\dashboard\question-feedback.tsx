import { CheckCircle, XCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';

interface QuestionFeedbackProps {
  isCorrect: boolean;
}

export function QuestionFeedback({ isCorrect }: QuestionFeedbackProps) {
  const t = useTranslations('Questions');
  return (
    <div 
      className={`flex items-center gap-2 text-sm font-medium ${
        isCorrect 
          ? 'text-green-600 dark:text-green-500' 
          : 'text-red-600 dark:text-red-500'
      }`}
    >
      {isCorrect ? (
        <>
          <CheckCircle className="h-5 w-5" />
          {t('correct')}
        </>
      ) : (
        <>
          <XCircle className="h-5 w-5" />
          {t('incorrect')}
        </>
      )}
    </div>
  );
}